<?php
/**
 * PayOp Plugin Status Checker
 * 
 * Add this code to your functions.php temporarily to check plugin status
 * Remove after testing
 */

// Only run this in admin and if user is administrator
if (is_admin() && current_user_can('administrator')) {
    add_action('admin_notices', 'payop_status_check');
}

function payop_status_check() {
    echo '<div class="notice notice-info"><h3>PayOp Plugin Status Check</h3>';
    
    // Check if WooCommerce is active
    if (class_exists('WooCommerce')) {
        echo '<p>✅ WooCommerce is active</p>';
    } else {
        echo '<p>❌ WooCommerce is not active</p>';
    }
    
    // Check if PayOp plugin is loaded
    if (class_exists('PayOp_WooCommerce')) {
        echo '<p>✅ PayOp plugin is loaded</p>';
    } else {
        echo '<p>❌ PayOp plugin is not loaded</p>';
    }
    
    // Check if gateway is registered
    $gateways = WC()->payment_gateways->get_available_payment_gateways();
    if (isset($gateways['payop'])) {
        echo '<p>✅ PayOp gateway is registered</p>';
        
        $gateway = $gateways['payop'];
        if ($gateway->enabled === 'yes') {
            echo '<p>✅ PayOp gateway is enabled</p>';
        } else {
            echo '<p>⚠️ PayOp gateway is disabled</p>';
        }
    } else {
        echo '<p>❌ PayOp gateway is not registered</p>';
    }
    
    // Check database tables
    global $wpdb;
    $payment_methods_table = $wpdb->prefix . 'payop_payment_methods';
    $transactions_table = $wpdb->prefix . 'payop_transactions';
    
    $tables_exist = ($wpdb->get_var("SHOW TABLES LIKE '$payment_methods_table'") == $payment_methods_table) &&
                    ($wpdb->get_var("SHOW TABLES LIKE '$transactions_table'") == $transactions_table);
    
    if ($tables_exist) {
        echo '<p>✅ Database tables exist</p>';
        
        // Count payment methods
        $method_count = $wpdb->get_var("SELECT COUNT(*) FROM $payment_methods_table");
        echo "<p>📊 Payment methods in database: $method_count</p>";
    } else {
        echo '<p>❌ Database tables missing</p>';
    }
    
    // Check assets
    $plugin_url = plugin_dir_url(dirname(__FILE__)) . 'payop-woo/';
    $js_file = $plugin_url . 'assets/js/payop-payment.js';
    $css_file = $plugin_url . 'assets/css/payop-payment.css';
    
    echo '<p>📂 Asset files:</p>';
    echo '<ul>';
    echo '<li>JS: <a href="' . $js_file . '" target="_blank">' . $js_file . '</a></li>';
    echo '<li>CSS: <a href="' . $css_file . '" target="_blank">' . $css_file . '</a></li>';
    echo '</ul>';
    
    // Check configuration
    $settings = get_option('woocommerce_payop_settings', array());
    if (!empty($settings['public_key'])) {
        echo '<p>✅ Public key configured</p>';
    } else {
        echo '<p>⚠️ Public key not configured</p>';
    }
    
    if (!empty($settings['secret_key'])) {
        echo '<p>✅ Secret key configured</p>';
    } else {
        echo '<p>⚠️ Secret key not configured</p>';
    }
    
    echo '<p><strong>Next Steps:</strong></p>';
    echo '<ol>';
    echo '<li>Go to <a href="' . admin_url('admin.php?page=wc-settings&tab=checkout&section=payop') . '">WooCommerce > Settings > Payments > PayOp</a></li>';
    echo '<li>Enable the gateway and configure your API credentials</li>';
    echo '<li>Go to <a href="' . admin_url('admin.php?page=payop-payment-methods') . '">WooCommerce > PayOp Methods</a></li>';
    echo '<li>Click "Sync Payment Methods" to load available methods</li>';
    echo '<li>Test the checkout process</li>';
    echo '</ol>';
    
    echo '</div>';
}
?>
