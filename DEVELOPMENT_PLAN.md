# PayOp WooCommerce Plugin Development Plan

## Executive Summary

This document outlines the comprehensive development plan for a WooCommerce payment gateway plugin that integrates with PayOp's payment aggregator service using their Direct Integration approach. The plugin will support 122 payment methods across 5 categories, handle dynamic field collection, and provide seamless checkout experience with direct provider redirects.

---

## EXECUTIVE SUMMARY: Critical Integration Requirements

### Key Findings from WooCommerce Documentation Analysis

1. **Event-Driven Architecture is Mandatory**
   - Must implement `onCheckoutValidation`, `onPaymentSetup`, `onCheckoutSuccess`, `onCheckoutFail` events
   - `onPaymentProcessing` is deprecated - use `onPaymentSetup` instead
   - Event responses must follow strict format: `{ type, message, messageContext, paymentMethodData }`

2. **Payment Method Registration Requirements**
   - Use `registerPaymentMethod` with specific prop structure
   - `canMakePayment` callback receives comprehensive cart/customer data
   - Support `ariaLabel`, `placeOrderButtonLabel`, and `supports` configuration
   - Server-side must extend `AbstractPaymentMethodType`

3. **Advanced Filtering Capabilities**
   - `registerPaymentMethodExtensionCallbacks` for dynamic payment method filtering
   - Payment requirements system for conditional method availability
   - Store API integration for server-side payment processing hooks

4. **Store API Integration**
   - Preferred processing method: `woocommerce_store_api_checkout_process_payment_with_context`
   - `PaymentContext` and `PaymentResult` objects for structured data handling
   - Legacy `process_payment` still supported but not recommended for blocks

5. **React Component Architecture**
   - Components receive standardized props: `eventRegistration`, `emitResponse`, `billing`, `cartData`, etc.
   - Must handle async operations properly in event callbacks
   - Error handling through validation errors and notice contexts

### Implementation Priority Matrix

**CRITICAL (Must Have)**
- ✅ Event lifecycle implementation (`onPaymentSetup`, `onCheckoutValidation`)
- ✅ Store API payment processing hook
- ✅ `registerPaymentMethod` with proper configuration
- ✅ Server-side `AbstractPaymentMethodType` class

**HIGH (Should Have)**
- ✅ Payment method filtering callbacks
- ✅ Advanced `canMakePayment` logic
- ✅ Payment requirements system
- ✅ Proper error handling and notices

**MEDIUM (Nice to Have)**
- Dynamic payment method grouping
- Saved payment methods support
- Express payment methods
- Advanced validation patterns

### Technical Debt Considerations

1. **Documentation Migration**: WooCommerce Blocks docs moved to monorepo - ensure tracking latest updates
2. **API Stability**: Some APIs marked as experimental - implement feature detection
3. **Performance**: Payment method filtering may impact large catalogs - implement caching
4. **Testing**: Block-based checkout requires different testing approach than classic checkout

---

## Project Specifications

### Technical Requirements
- **PHP Version**: 8.2+
- **WordPress**: Latest stable version (6.0+)
- **WooCommerce**: Latest stable version (8.0+) with block-based checkout support
- **Architecture**: WooCommerce HPOS (High-Performance Order Storage) compatible
- **Checkout Type**: Block-based checkout implementation
- **Payment Methods**: 122 methods across 5 categories
- **Geographic Coverage**: 226+ countries
- **Currency Support**: 8 currencies (EUR, USD, PHP, GBP, CAD, AUD, BRL, DKK)

### PayOp API Analysis Summary
- **Total Payment Methods**: 122
- **Bank Transfers**: 68 methods (55.7%)
- **Cash Payments**: 42 methods (34.4%)
- **E-Wallets**: 10 methods (8.2%)
- **International Cards**: 1 method (0.8%)
- **Cryptocurrency**: 1 method (0.8%)

### Field Requirements Analysis
- **Email**: Required by all 122 methods
- **Name**: Required by 120 methods (98.4%)
- **Phone**: Required by 48 methods (39.3%)
- **Document**: Required by 37 methods (30.3%) - primarily Latin America
- **Date of Birth**: Required by 18 methods (14.8%) - primarily European banking
- **Bank Code**: Required by 14 methods (11.5%)
- **Bank Type**: Required by 14 methods (11.5%)

## Development Phases

### Phase 1: Foundation Setup (Week 1-2)
**Duration**: 2 weeks
**Complexity**: Medium

#### 1.1 Project Structure & Environment
- **Deliverable**: Complete plugin directory structure
- **Tasks**:
  - Create WordPress plugin boilerplate following WordPress coding standards
  - Set up composer for dependency management
  - Configure PHPUnit for testing
  - Implement PSR-4 autoloading
  - Create development environment with WordPress + WooCommerce

**File Structure**:
```
payop-woocommerce/
├── payop-woocommerce.php              # Main plugin file
├── composer.json                       # Dependencies
├── includes/
│   ├── class-payop-wc-gateway.php     # Main gateway class
│   ├── class-payop-api-client.php     # API communication
│   ├── class-payop-signature.php      # Signature generation
│   ├── class-payop-field-manager.php  # Dynamic field handling
│   ├── class-payop-payment-methods.php # Payment method management
│   ├── admin/
│   │   ├── class-payop-admin.php      # Admin panel
│   │   └── views/                     # Admin templates
│   ├── blocks/                        # Checkout block components
│   └── webhooks/
│       └── class-payop-ipn-handler.php # IPN processing
├── assets/
│   ├── js/                           # JavaScript files
│   ├── css/                          # Stylesheets
│   └── images/                       # Payment method logos
├── languages/                        # Internationalization
├── tests/                           # Unit tests
└── readme.txt                       # WordPress plugin readme
```

#### 1.2 Core Plugin Infrastructure
- **Main Plugin File**: WordPress plugin header, activation/deactivation hooks
- **Autoloading**: PSR-4 compliant class loading
- **Database Setup**: Custom tables for payment method cache and transaction logs
- **Security**: Nonce verification, capability checks, input sanitization

**Custom Database Tables**:
```sql
-- Payment methods cache
CREATE TABLE {prefix}_payop_payment_methods (
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    identifier int(11) NOT NULL,
    type varchar(50) NOT NULL,
    title varchar(255) NOT NULL,
    logo varchar(500),
    currencies text,
    countries text,
    config text,
    is_enabled tinyint(1) DEFAULT 1,
    display_order int(11) DEFAULT 0,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY identifier (identifier),
    KEY type (type),
    KEY is_enabled (is_enabled)
);

-- Transaction logs
CREATE TABLE {prefix}_payop_transactions (
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    order_id bigint(20) unsigned NOT NULL,
    invoice_id varchar(100),
    transaction_id varchar(100),
    payment_method_id int(11),
    amount decimal(10,2),
    currency varchar(3),
    status varchar(20),
    customer_data text,
    api_response text,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY order_id (order_id),
    KEY invoice_id (invoice_id),
    KEY transaction_id (transaction_id),
    KEY status (status)
);
```

### Phase 2: PayOp API Integration (Week 3-4)
**Duration**: 2 weeks
**Complexity**: High

#### 2.1 API Client Development
- **Deliverable**: Complete API client with all required endpoints
- **Security**: SHA-256 signature generation, JWT token management
- **Error Handling**: Comprehensive error handling for all documented response codes

**API Client Features**:
```php
class PayOp_API_Client {
    private $base_url = 'https://api.payop.com';
    private $public_key;
    private $secret_key;
    private $jwt_token;
    
    // Core API methods
    public function get_payment_methods($application_id);
    public function create_invoice($order_data);
    public function get_invoice($invoice_id);
    public function create_checkout($checkout_data);
    public function check_invoice_status($invoice_id);
    public function get_transaction($transaction_id);
    public function void_transaction($invoice_id);
    
    // Security methods
    private function generate_signature($amount, $currency, $order_id);
    private function make_request($endpoint, $data = null, $method = 'GET');
    private function validate_response($response);
}
```

#### 2.2 Payment Method Management
- **Deliverable**: Dynamic payment method loading and caching system
- **Caching**: Local database caching with configurable refresh intervals
- **Filtering**: Currency, country, and admin preference-based filtering

**Payment Method Manager**:
```php
class PayOp_Payment_Methods {
    // Fetch and cache payment methods
    public function refresh_payment_methods();
    public function get_available_methods($currency, $country = null);
    public function get_method_by_identifier($identifier);
    
    // Admin management
    public function enable_method($identifier);
    public function disable_method($identifier);
    public function set_method_order($identifier, $order);
    public function create_method_groups();
    
    // Customer filtering
    public function filter_by_currency($methods, $currency);
    public function filter_by_country($methods, $country);
    public function sort_by_priority($methods);
}
```

#### 2.3 Dynamic Field System
- **Deliverable**: Field management system handling 122+ payment method configurations
- **Validation**: Regex pattern validation for documents, bank codes, dates
- **UI Generation**: Dynamic form field generation based on API configuration

**Field Manager Features**:
```php
class PayOp_Field_Manager {
    // Field type handlers
    private $field_types = [
        'email' => 'render_email_field',
        'text' => 'render_text_field',
        'bank_code' => 'render_bank_code_field',
        'bank_type' => 'render_bank_type_field',
    ];
    
    public function get_required_fields($payment_method_id);
    public function render_fields($payment_method_id, $values = []);
    public function validate_fields($payment_method_id, $data);
    public function sanitize_field_data($field_config, $value);
}
```

### Phase 3: WooCommerce Gateway Implementation (Week 5-6)
**Duration**: 2 weeks
**Complexity**: High

#### 3.1 Payment Gateway Class
- **Deliverable**: Complete WooCommerce payment gateway implementation
- **Compliance**: Full WooCommerce Payment Gateway API compliance
- **HPOS Support**: High-Performance Order Storage compatibility

**Gateway Class Structure**:
```php
class PayOp_WC_Gateway extends WC_Payment_Gateway {
    public function __construct();
    public function init_form_fields();        // Admin settings
    public function process_payment($order_id); // Payment processing
    public function process_refund($order_id, $amount = null, $reason = '');
    public function webhook();                 // IPN handler
    
    // Custom methods
    private function create_payop_invoice($order);
    private function redirect_to_payment($invoice_id, $payment_method, $customer_data);
    private function handle_payment_return($order_id);
    private function update_order_status($order, $status, $note = '');
}
```

#### 3.2 Order Processing Logic
- **Invoice Creation**: PayOp invoice creation with proper signature
- **Customer Data**: Collection and validation of payment method-specific fields
- **Redirect Handling**: Direct redirect to payment provider pages
- **Status Management**: Order status updates based on payment states

**Updated Payment Processing Flow (Based on Store API)**:
```php
public function process_payment($order_id) {
    $order = wc_get_order($order_id);
    
    // 1. Create PayOp invoice
    $invoice_data = $this->prepare_invoice_data($order);
    $invoice = $this->api_client->create_invoice($invoice_data);
    
    // 2. Get selected payment method and customer data from $_POST (converted by Store API)
    $payment_method_id = $_POST['payop_payment_method'] ?? null;
    $customer_data = $_POST['payop_customer_data'] ?? [];
    
    // 3. Create checkout transaction
    $checkout_data = $this->prepare_checkout_data($invoice['data'], $customer_data, $payment_method_id);
    $checkout = $this->api_client->create_checkout($checkout_data);
    
    // 4. Store transaction data
    $this->store_transaction_data($order_id, $invoice['data'], $checkout['data'], $customer_data);
    
    // 5. Set order to pending payment
    $order->update_status('pending', 'Awaiting PayOp payment');
    
    // 6. Return redirect URL for direct provider redirect
    return [
        'result' => 'success',
        'redirect' => $this->get_payment_redirect_url($checkout['data'])
    ];
}

// Store API Integration (Following WooCommerce Documentation)
public function initialize() {
    add_action(
        'woocommerce_rest_checkout_process_payment_with_context',
        [$this, 'process_payment_via_store_api'],
        10,
        2
    );
}

public function process_payment_via_store_api($context, $result) {
    if ($context->payment_method !== 'payop') {
        return;
    }
    
    $order = $context->order;
    $payment_data = $context->payment_data;
    
    try {
        // Create PayOp invoice and checkout
        $payment_result = $this->process_payop_payment($order, $payment_data);
        
        if ($payment_result['success']) {
            $result->set_status('success');
            $result->set_payment_details([
                'payop_invoice_id' => $payment_result['invoice_id'],
                'payop_transaction_id' => $payment_result['transaction_id']
            ]);
            $result->set_redirect_url($payment_result['redirect_url']);
        } else {
            $result->set_status('failure');
        }
    } catch (Exception $e) {
        $result->set_status('error');
        throw $e;
    }
}
```

#### 3.3 Admin Configuration Panel
- **Deliverable**: Comprehensive admin settings interface
- **Features**: API credentials, payment method management, grouping options
- **Validation**: Real-time API credential validation

**Admin Settings Structure**:
```php
public function init_form_fields() {
    $this->form_fields = [
        'enabled' => [
            'title' => 'Enable PayOp',
            'type' => 'checkbox',
            'default' => 'no'
        ],
        'title' => [
            'title' => 'Title',
            'type' => 'text',
            'default' => 'PayOp Payment Gateway'
        ],
        'public_key' => [
            'title' => 'Public Key',
            'type' => 'text',
            'description' => 'Your PayOp public key'
        ],
        'secret_key' => [
            'title' => 'Secret Key',
            'type' => 'password',
            'description' => 'Your PayOp secret key'
        ],
        'jwt_token' => [
            'title' => 'JWT Token',
            'type' => 'textarea',
            'description' => 'Your PayOp JWT token'
        ],
        'payment_method_grouping' => [
            'title' => 'Payment Method Grouping',
            'type' => 'select',
            'options' => [
                'none' => 'No Grouping',
                'type' => 'Group by Type',
                'country' => 'Group by Country',
                'currency' => 'Group by Currency'
            ]
        ]
    ];
}
```

### Phase 4: Block-Based Checkout Integration (Week 7-8)
**Duration**: 2 weeks
**Complexity**: High

#### 4.1 React Components Development
- **Deliverable**: React components for WooCommerce Checkout Blocks
- **Features**: Dynamic payment method selection, field rendering, validation
- **Compatibility**: WooCommerce Blocks API compliance

**Updated React Component Structure (Based on WooCommerce registerPaymentMethod standards)**:
```jsx
// PayOpPaymentMethod.js - Following WooCommerce registerPaymentMethod standards
const { registerPaymentMethod } = window.wc.wcBlocksRegistry;

const PayOpPaymentMethod = ({ 
    eventRegistration, 
    emitResponse, 
    activePaymentMethod,
    billing,
    cartData,
    checkoutStatus,
    components,
    onSubmit,
    paymentStatus,
    shippingData,
    shouldSavePayment 
}) => {
    const [paymentMethods, setPaymentMethods] = useState([]);
    const [selectedMethod, setSelectedMethod] = useState(null);
    const [customerData, setCustomerData] = useState({});
    const [fieldErrors, setFieldErrors] = useState({});
    
    const { onPaymentSetup } = eventRegistration;
    const { PaymentMethodLabel, ValidationInputError } = components;
    
    useEffect(() => {
        const unsubscribe = onPaymentSetup(async () => {
            const isDataValid = validateCustomerData();
            
            if (isDataValid) {
                return {
                    type: emitResponse.responseTypes.SUCCESS,
                    meta: {
                        paymentMethodData: {
                            payop_payment_method: selectedMethod,
                            payop_customer_data: customerData
                        }
                    }
                };
            }
            
            return {
                type: emitResponse.responseTypes.ERROR,
                message: 'Please complete all required fields'
            };
        });
        
        return () => unsubscribe();
    }, [onPaymentSetup, emitResponse, selectedMethod, customerData]);
    
    const validateCustomerData = () => {
        const errors = {};
        if (!selectedMethod) {
            errors.method = 'Please select a payment method';
            return false;
        }
        
        const requiredFields = getRequiredFields(selectedMethod);
        requiredFields.forEach(field => {
            if (!customerData[field.name] && field.required) {
                errors[field.name] = `${field.title || field.name} is required`;
            }
            
            // Regex validation
            if (customerData[field.name] && field.regexp) {
                if (!new RegExp(field.regexp).test(customerData[field.name])) {
                    errors[field.name] = `Invalid format for ${field.title || field.name}`;
                }
            }
        });
        
        setFieldErrors(errors);
        return Object.keys(errors).length === 0;
    };
    
    const canMakePayment = ({ cart, cartTotals, billingAddress }) => {
        // Filter payment methods by currency and country
        const storeCurrency = cartTotals?.currency_code;
        const customerCountry = billingAddress?.country;
        const cartTotal = cartTotals?.total_price;
        
        return paymentMethods.some(method => {
            // Currency filtering
            if (!method.currencies.includes(storeCurrency)) {
                return false;
            }
            
            // Country filtering
            if (method.countries.length && !method.countries.includes(customerCountry)) {
                return false;
            }
            
            // Amount filtering (if PayOp has limits)
            if (method.min_amount && cartTotal < method.min_amount) {
                return false;
            }
            
            if (method.max_amount && cartTotal > method.max_amount) {
                return false;
            }
            
            // Shipping requirements
            if (method.requires_shipping && !cartNeedsShipping) {
                return false;
            }
            
            return true;
        });
    };
    
    return (
        <div className="wc-block-components-payment-method-config">
            <PayOpMethodSelector
                methods={paymentMethods}
                selected={selectedMethod}
                onSelect={setSelectedMethod}
                currency={billing.currency}
                country={billing.billingAddress.country}
            />
            {selectedMethod && (
                <PayOpFieldContainer>
                    {renderDynamicFields(selectedMethod)}
                </PayOpFieldContainer>
            )}
        </div>
    );
};

// Registration following WooCommerce standards
registerPaymentMethod({
    name: 'payop',
    label: <PaymentMethodLabel text="PayOp Payment Gateway" />,
    content: <PayOpPaymentMethod />,
    edit: <PayOpPaymentMethod />,
    canMakePayment: ({ cart, cartTotals, billingAddress }) => {
        // Payment method availability logic
        return true;
    },
    ariaLabel: 'PayOp Payment Gateway',
    supports: {
        features: ['products'],
        showSavedCards: false,
        showSaveOption: false
    }
});
```

#### 4.2 Checkout Event Lifecycle Implementation

**Critical Requirements from WooCommerce Checkout Flow Documentation:**

#### Event-Driven Architecture
PayOp integration must implement comprehensive event handling following WooCommerce's event emitter system:

```javascript
const PayOpPaymentMethod = ({ 
    eventRegistration, 
    emitResponse,
    billing,
    checkoutStatus,
    paymentStatus,
    // ... other props
}) => {
    const { 
        onPaymentSetup, 
        onCheckoutValidation, 
        onCheckoutSuccess, 
        onCheckoutFail 
    } = eventRegistration;
    
    // Payment setup event (replaces deprecated onPaymentProcessing)
    useEffect(() => {
        const unsubscribe = onPaymentSetup(async () => {
            // Validate payment data
            const validation = await validatePaymentData();
            
            if (!validation.isValid) {
                return {
                    type: emitResponse.responseTypes.ERROR,
                    message: validation.errorMessage,
                    messageContext: emitResponse.noticeContexts.PAYMENTS
                };
            }
            
            // Return payment data for server processing
            return {
                type: emitResponse.responseTypes.SUCCESS,
                meta: {
                    paymentMethodData: {
                        payop_payment_method: selectedMethod?.identifier,
                        payop_customer_data: customerData,
                        payop_validation_token: validation.token
                    },
                    billingAddress: validation.updatedBilling, // For express payments
                    shippingAddress: validation.updatedShipping // For express payments
                }
            };
        });
        
        return unsubscribe;
    }, [onPaymentSetup, emitResponse, selectedMethod, customerData]);
    
    // Checkout validation event
    useEffect(() => {
        const unsubscribe = onCheckoutValidation(() => {
            // Pre-checkout validation
            const errors = validateAllFields();
            
            if (errors.length > 0) {
                return {
                    errorMessage: 'Please fix the payment method errors',
                    validationErrors: errors
                };
            }
            
            return true;
        });
        
        return unsubscribe;
    }, [onCheckoutValidation]);
    
    // Success and failure handlers
    useEffect(() => {
        const unsubscribeSuccess = onCheckoutSuccess(({ paymentResult }) => {
            // Handle successful payment
            if (paymentResult.paymentStatus === 'success') {
                // Cleanup, analytics, etc.
                trackPaymentSuccess(paymentResult.paymentDetails);
            }
        });
        
        const unsubscribeFail = onCheckoutFail(() => {
            // Handle failed payment
            resetPaymentState();
            return true;
        });
        
        return () => {
            unsubscribeSuccess();
            unsubscribeFail();
        };
    }, [onCheckoutSuccess, onCheckoutFail]);
    
    // Component rendering...
};
```

#### 4.3 Advanced Payment Method Registration

**Enhanced Registration Following WooCommerce Standards:**

```javascript
// PayOp Payment Method Registration
registerPaymentMethod({
    name: 'payop',
    label: (
        <div className="payop-payment-label">
            <img src={payopLogo} alt="PayOp" className="payop-logo" />
            <span>PayOp Payment Gateway</span>
        </div>
    ),
    content: <PayOpPaymentMethod />,
    edit: <PayOpPaymentMethodEdit />,
    canMakePayment: ({ cart, cartTotals, cartNeedsShipping, billingAddress, shippingAddress, paymentRequirements }) => {
        // Advanced availability logic based on PayOp capabilities
        const currency = cartTotals?.currency_code;
        const country = billingAddress?.country;
        const amount = cartTotals?.total_price;
        
        // Check if PayOp supports the store's currency
        if (!payopSupportedCurrencies.includes(currency)) {
            return false;
        }
        
        // Check country restrictions
        if (payopRestrictedCountries.includes(country)) {
            return false;
        }
        
        // Check amount limits
        if (amount < payopMinAmount || amount > payopMaxAmount) {
            return false;
        }
        
        // Check payment requirements (for advanced filtering)
        if (paymentRequirements.includes('payop_direct_only')) {
            return payopConfig.directIntegrationEnabled;
        }
        
        return true;
    },
    ariaLabel: 'PayOp Payment Gateway - Multiple payment methods available',
    placeOrderButtonLabel: 'Process Payment with PayOp',
    supports: {
        features: ['products'],
        showSavedCards: false, // PayOp doesn't support saved cards initially
        showSaveOption: false
    },
    // Advanced props for payment method components
    savedTokenComponent: null, // Not implemented initially
});
```

#### 4.4 Payment Method Filtering Implementation

**Based on WooCommerce Payment Method Filtering Documentation:**

```javascript
// Register extension callbacks for advanced filtering
import { registerPaymentMethodExtensionCallbacks } from '@woocommerce/blocks-registry';

registerPaymentMethodExtensionCallbacks('payop-payment-gateway', {
    payop: ({ cart, cartTotals, billingAddress, paymentRequirements }) => {
        // Advanced filtering logic based on cart contents
        
        // 1. Currency-based filtering
        const supportedCurrencies = ['USD', 'EUR', 'GBP', 'UAH', 'RUB'];
        if (!supportedCurrencies.includes(cartTotals.currency_code)) {
            return false;
        }
        
        // 2. Geographic filtering
        const restrictedCountries = ['US-sanctioned', 'high-risk-countries'];
        if (restrictedCountries.includes(billingAddress.country)) {
            return false;
        }
        
        // 3. Product-based filtering
        const hasDigitalProducts = cart.items.some(item => 
            item.meta_data?.some(meta => meta.key === 'digital_product' && meta.value === 'yes')
        );
        
        const hasPhysicalProducts = cart.items.some(item => 
            item.meta_data?.some(meta => meta.key === 'physical_product' && meta.value === 'yes')
        );
        
        // 4. Shipping-based filtering
        if (cartNeedsShipping && !payopConfig.supportsShipping) {
            return false;
        }
        
        // 5. Amount-based filtering
        const totalAmount = parseFloat(cartTotals.total_price);
        if (totalAmount < payopConfig.minAmount || totalAmount > payopConfig.maxAmount) {
            return false;
        }
        
        // 6. Payment requirements filtering (advanced feature)
        if (paymentRequirements.includes('instant_payment_only')) {
            // Only show if PayOp methods support instant payment
            return payopConfig.hasInstantPaymentMethods;
        }
        
        return true;
    }
});
```

#### 4.5 Store API Integration and Server-Side Processing

**Enhanced Server-Side Integration:**

```php
<?php
// PayOp Payment Method Type Class
class PayOp_Payment_Method_Type extends AbstractPaymentMethodType {
    
    protected $name = 'payop';
    
    public function initialize() {
        // Initialize PayOp API client
        $this->payop_api = new PayOp_API_Client(
            get_option('payop_public_key'),
            get_option('payop_secret_key'),
            get_option('payop_jwt_token')
        );
        
        // Register Store API processing hooks
        add_action('woocommerce_store_api_checkout_process_payment_with_context', 
                   [$this, 'process_payment_with_context'], 10, 2);
    }
    
    public function is_active() {
        return get_option('payop_enabled') === 'yes' && 
               !empty(get_option('payop_public_key')) &&
               !empty(get_option('payop_secret_key'));
    }
    
    public function get_payment_method_script_handles() {
        wp_register_script(
            'payop-payment-method',
            plugins_url('assets/js/payop-payment-method.js', __FILE__),
            [
                'wc-blocks-registry',
                'wc-settings',
                'wp-element',
                'wp-html-entities',
                'wp-i18n',
            ],
            '1.0.0',
            true
        );
        
        return ['payop-payment-method'];
    }
    
    public function get_payment_method_script_handles_for_admin() {
        wp_register_script(
            'payop-payment-method-admin',
            plugins_url('assets/js/payop-payment-method-admin.js', __FILE__),
            [
                'wc-blocks-registry',
                'wc-settings',
                'wp-element',
                'wp-html-entities',
                'wp-i18n',
            ],
            '1.0.0',
            true
        );
        
        return ['payop-payment-method-admin'];
    }
    
    public function get_payment_method_data() {
        return [
            'title' => get_option('payop_title', 'PayOp Payment Gateway'),
            'description' => get_option('payop_description', ''),
            'supports' => ['products'],
            'logo_url' => plugins_url('assets/images/payop-logo.png', __FILE__),
            'payment_methods' => $this->get_available_payment_methods(),
            'config' => [
                'api_endpoint' => $this->payop_api->get_api_endpoint(),
                'public_key' => get_option('payop_public_key'),
                'supported_currencies' => $this->get_supported_currencies(),
                'min_amount' => get_option('payop_min_amount', 0),
                'max_amount' => get_option('payop_max_amount', 999999),
                'direct_integration_enabled' => get_option('payop_direct_integration') === 'yes',
                'test_mode' => get_option('payop_test_mode') === 'yes'
            ]
        ];
    }
    
    /**
     * Process payment using Store API context
     * This is the preferred method for WooCommerce Blocks
     */
    public function process_payment_with_context($payment_context, $payment_result) {
        if ($payment_context->payment_method !== $this->name) {
            return;
        }
        
        try {
            $payment_data = $payment_context->payment_data;
            $order = $payment_context->order;
            
            // Extract PayOp-specific data
            $payop_method_id = $payment_data['payop_method_id'] ?? '';
            $payop_customer_data = $payment_data['payop_customer_data'] ?? [];
            $payop_checkout_data = $payment_data['payop_checkout_data'] ?? [];
            
            // Create PayOp invoice
            $invoice_data = [
                'publicKey' => get_option('payop_public_key'),
                'order' => [
                    'number' => $order->get_order_number(),
                    'amount' => $order->get_total(),
                    'currency' => $order->get_currency(),
                    'items' => $this->prepare_order_items($order),
                    'description' => sprintf('Order #%s', $order->get_order_number())
                ],
                'payer' => [
                    'email' => $order->get_billing_email(),
                    'name' => $order->get_billing_first_name() . ' ' . $order->get_billing_last_name(),
                    'phone' => $order->get_billing_phone()
                ],
                'paymentMethod' => $payop_method_id,
                'customerData' => $payop_customer_data,
                'resultUrl' => $this->get_return_url($order),
                'failPath' => $this->get_cancel_url($order),
                'notificationUrl' => $this->get_ipn_url($order)
            ];
            
            $response = $this->payop_api->create_invoice($invoice_data);
            
            if ($response && $response['status'] === 'success') {
                // Update order meta
                $order->update_meta_data('_payop_invoice_id', $response['data']['id']);
                $order->update_meta_data('_payop_payment_method', $payop_method_id);
                $order->save();
                
                // Set payment result
                $payment_result->set_status('success');
                $payment_result->set_redirect_url($response['data']['directUrl']);
                $payment_result->set_payment_details([
                    'payop_invoice_id' => $response['data']['id'],
                    'payop_redirect_url' => $response['data']['directUrl'],
                    'payop_method' => $payop_method_id
                ]);
                
            } else {
                $payment_result->set_status('failure');
                $payment_result->set_payment_details([
                    'payop_error' => $response['message'] ?? 'Payment processing failed'
                ]);
            }
            
        } catch (Exception $e) {
            $payment_result->set_status('error');
            $payment_result->set_payment_details([
                'payop_error' => $e->getMessage()
            ]);
        }
    }
    
    private function get_available_payment_methods() {
        // Cache payment methods for performance
        $cache_key = 'payop_payment_methods_' . md5(get_option('payop_public_key'));
        $methods = get_transient($cache_key);
        
        if (false === $methods) {
            try {
                $methods = $this->payop_api->get_payment_methods();
                set_transient($cache_key, $methods, HOUR_IN_SECONDS);
            } catch (Exception $e) {
                // Fallback to cached methods or empty array
                $methods = get_option('payop_fallback_methods', []);
            }
        }
        
        return $methods;
    }
    
    private function get_supported_currencies() {
        // Get currencies from PayOp API or configuration
        return ['USD', 'EUR', 'GBP', 'UAH', 'RUB', 'PLN', 'CZK', 'BGN', 'RON', 'HUF'];
    }
}

// Register the payment method type
add_action('woocommerce_blocks_loaded', function() {
    if (class_exists('Automattic\WooCommerce\Blocks\Payments\Integrations\AbstractPaymentMethodType')) {
        add_action('woocommerce_blocks_payment_method_type_registration', function($payment_method_registry) {
            $payment_method_registry->register(new PayOp_Payment_Method_Type());
        });
    }
});
```

#### 4.5 Advanced Payment Requirements System

**Implementation of WooCommerce Payment Requirements:**

```php
// Register payment requirements for advanced filtering
add_action('woocommerce_blocks_loaded', function() {
    woocommerce_store_api_register_payment_requirements([
        'data_callback' => 'payop_inject_payment_requirements'
    ]);
});

function payop_inject_payment_requirements() {
    $requirements = [];
    
    // Check for high-value transactions
    $cart_total = WC()->cart->get_total('raw');
    if ($cart_total > 1000) {
        $requirements[] = 'high_value_transaction';
    }
    
    // Check for digital products
    foreach (WC()->cart->get_cart() as $cart_item) {
        $product = $cart_item['data'];
        if ($product->is_virtual()) {
            $requirements[] = 'digital_products';
            break;
        }
    }
    
    // Check for subscription products
    if (class_exists('WC_Subscriptions_Cart') && WC_Subscriptions_Cart::cart_contains_subscription()) {
        $requirements[] = 'subscription_products';
    }
    
    // Check customer location
    $customer = WC()->customer;
    if ($customer && in_array($customer->get_billing_country(), ['US', 'CA', 'GB'])) {
        $requirements[] = 'tier1_countries';
    }
    
    return $requirements;
}
```

---

## Technical Implementation Details

### Payment Flow Architecture

```mermaid
sequenceDiagram
    participant Customer
    participant WooCommerce
    participant PayOp Plugin
    participant PayOp API
    participant Payment Provider
    
    Customer->>WooCommerce: Add to cart & checkout
    WooCommerce->>PayOp Plugin: Initialize payment
    PayOp Plugin->>PayOp API: Get available payment methods
    PayOp API-->>PayOp Plugin: Return 122 payment methods
    PayOp Plugin-->>Customer: Show payment options
    Customer->>PayOp Plugin: Select payment method
    PayOp Plugin-->>Customer: Show required fields
    Customer->>PayOp Plugin: Fill required information
    PayOp Plugin->>PayOp API: Create invoice
    PayOp API-->>PayOp Plugin: Return invoice ID
    PayOp Plugin->>PayOp API: Create checkout transaction
    PayOp API-->>PayOp Plugin: Return transaction ID
    PayOp Plugin-->>Customer: Redirect to payment provider
    Customer->>Payment Provider: Complete payment
    Payment Provider->>PayOp API: Payment notification
    PayOp API->>PayOp Plugin: IPN notification
    PayOp Plugin->>WooCommerce: Update order status
```

### Database Schema

#### Payment Methods Cache
```sql
CREATE TABLE wp_payop_payment_methods (
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    identifier int(11) NOT NULL UNIQUE,
    type varchar(50) NOT NULL,
    form_type varchar(50) DEFAULT 'standard',
    title varchar(255) NOT NULL,
    logo varchar(500),
    currencies text, -- JSON array
    countries text,  -- JSON array
    config text,     -- JSON object with fields
    is_enabled tinyint(1) DEFAULT 1,
    display_order int(11) DEFAULT 0,
    group_id int(11),
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY type (type),
    KEY is_enabled (is_enabled),
    KEY display_order (display_order)
);
```

#### Transaction Logs
```sql
CREATE TABLE wp_payop_transactions (
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    order_id bigint(20) unsigned NOT NULL,
    invoice_id varchar(100),
    transaction_id varchar(100),
    payment_method_id int(11),
    payment_method_identifier int(11),
    amount decimal(10,2),
    currency varchar(3),
    status varchar(20),
    customer_data text, -- JSON object
    api_request text,   -- JSON object
    api_response text,  -- JSON object
    ipn_data text,      -- JSON object
    error_message text,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY order_id (order_id),
    KEY invoice_id (invoice_id),
    KEY transaction_id (transaction_id),
    KEY status (status),
    KEY payment_method_id (payment_method_id)
);
```

#### Payment Method Groups
```sql
CREATE TABLE wp_payop_method_groups (
    id int(11) NOT NULL AUTO_INCREMENT,
    name varchar(100) NOT NULL,
    display_name varchar(200) NOT NULL,
    description text,
    group_type varchar(50) DEFAULT 'custom', -- type, country, currency, custom
    criteria text, -- JSON object with grouping criteria
    display_order int(11) DEFAULT 0,
    is_enabled tinyint(1) DEFAULT 1,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY group_type (group_type),
    KEY is_enabled (is_enabled)
);
```

### Security Implementation

#### Signature Generation
```php
class PayOp_Security {
    public static function generate_signature($amount, $currency, $order_id, $secret_key) {
        $string = sprintf('%s:%s:%s:%s', $amount, $currency, $order_id, $secret_key);
        return hash('sha256', $string);
    }
    
    public static function validate_ipn_source($ip) {
        $valid_ips = [
            '*************',
            '*************',
            '************',
            '*************'
        ];
        
        return in_array($ip, $valid_ips);
    }
    
    public static function sanitize_customer_data($data, $field_config) {
        $sanitized = [];
        
        foreach ($field_config as $field) {
            $value = isset($data[$field['name']]) ? $data[$field['name']] : '';
            
            switch ($field['type']) {
                case 'email':
                    $sanitized[$field['name']] = sanitize_email($value);
                    break;
                case 'text':
                    $sanitized[$field['name']] = sanitize_text_field($value);
                    break;
                default:
                    $sanitized[$field['name']] = sanitize_text_field($value);
            }
            
            // Apply regex validation if specified
            if (!empty($field['regexp']) && !preg_match('/' . $field['regexp'] . '/', $sanitized[$field['name']])) {
                throw new InvalidArgumentException(sprintf('Invalid format for field %s', $field['name']));
            }
        }
        
        return $sanitized;
    }
}
```

### Error Handling Strategy

#### API Error Handling
```php
class PayOp_Error_Handler {
    private static $error_codes = [
        401 => 'Invalid credentials',
        403 => 'Access denied',
        404 => 'Resource not found',
        422 => 'Validation error',
        500 => 'Server error'
    ];
    
    public static function handle_api_error($response) {
        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        
        if ($status_code >= 400) {
            $error_data = json_decode($body, true);
            $error_message = isset($error_data['message']) ? $error_data['message'] : 'Unknown error';
            
            if (is_array($error_message)) {
                // Validation errors
                $formatted_errors = [];
                foreach ($error_message as $field => $messages) {
                    $formatted_errors[] = sprintf('%s: %s', $field, implode(', ', $messages));
                }
                $error_message = implode('; ', $formatted_errors);
            }
            
            throw new PayOp_API_Exception($error_message, $status_code);
        }
        
        return json_decode($body, true);
    }
    
    public static function log_error($context, $message, $data = null) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log(sprintf('[PayOp %s] %s', $context, $message));
            if ($data) {
                error_log('[PayOp Data] ' . print_r($data, true));
            }
        }
    }
}
```

## Risk Assessment & Mitigation

### High-Risk Areas

#### 1. API Integration Complexity
- **Risk**: 122 payment methods with varying field requirements
- **Mitigation**: Comprehensive field mapping system, extensive testing
- **Contingency**: Fallback to simplified field collection for unknown methods

#### 2. Dynamic Field Validation
- **Risk**: Complex regex patterns and validation rules
- **Mitigation**: Server-side validation with client-side preview
- **Contingency**: Progressive enhancement with basic validation fallback

#### 3. IPN Reliability
- **Risk**: Webhook delivery failures, network issues
- **Mitigation**: Status polling backup system, comprehensive logging
- **Contingency**: Manual order status reconciliation tools

#### 4. Payment Method Changes
- **Risk**: PayOp adding/removing payment methods
- **Mitigation**: Automatic refresh mechanism, graceful degradation
- **Contingency**: Admin notification system for method changes

### Medium-Risk Areas

#### 1. WooCommerce Blocks Compatibility
- **Risk**: Breaking changes in WooCommerce Blocks API
- **Mitigation**: Version compatibility checks, extensive testing
- **Contingency**: Fallback to classic checkout if needed

#### 2. Performance with 122 Payment Methods
- **Risk**: Slow loading times, database performance
- **Mitigation**: Efficient caching, lazy loading, database optimization
- **Contingency**: Method filtering and pagination

### Low-Risk Areas

#### 1. WordPress/PHP Compatibility
- **Risk**: PHP version compatibility issues
- **Mitigation**: PHP 8.2+ requirement, compatibility testing
- **Contingency**: Polyfills for older PHP features

## Timeline Summary

| Phase | Duration | Key Deliverables | Dependencies |
|-------|----------|------------------|--------------|
| 1 | 2 weeks | Plugin foundation, database schema | - |
| 2 | 2 weeks | PayOp API integration, payment method management | Phase 1 |
| 3 | 2 weeks | WooCommerce gateway implementation | Phase 1, 2 |
| 4 | 2 weeks | Block-based checkout integration | Phase 3 |
| 5 | 1 week | IPN and webhook handling | Phase 2, 3 |
| 6 | 1 week | Advanced admin interface, grouping | Phase 2 |
| 7 | 1 week | Testing and quality assurance | All phases |
| 8 | 1 week | Documentation and deployment | All phases |

**Total Duration**: 12 weeks
**Estimated Effort**: 480 hours (40 hours/week)

## Success Criteria

### Functional Requirements
- [ ] Support for all 122 PayOp payment methods
- [ ] Dynamic field collection based on payment method requirements
- [ ] Successful payment processing with direct provider redirects
- [ ] Real-time order status updates via IPN
- [ ] Admin panel for payment method management
- [ ] WooCommerce Blocks compatibility
- [ ] HPOS (High-Performance Order Storage) support

### Performance Requirements
- [ ] Payment method loading time < 2 seconds
- [ ] Checkout process completion < 30 seconds
- [ ] Database queries optimized for large payment method datasets
- [ ] JavaScript bundle size < 100KB after minification

### Security Requirements
- [ ] Secure signature generation and validation
- [ ] IP whitelist validation for IPN
- [ ] Proper input sanitization and validation
- [ ] No sensitive data exposure in frontend
- [ ] Compliance with WordPress security standards

### User Experience Requirements
- [ ] Intuitive payment method selection
- [ ] Clear error messages and validation feedback
- [ ] Mobile-responsive checkout interface
- [ ] Consistent styling with WooCommerce themes
- [ ] Minimal friction checkout process

This comprehensive development plan provides a systematic approach to implementing the PayOp WooCommerce plugin, ensuring all technical requirements are met while maintaining high code quality and user experience standards.

## Additional Implementation Notes

### WooCommerce Blocks Extensibility Compliance

Based on comprehensive analysis of the [WooCommerce Blocks extensibility documentation](https://github.com/woocommerce/woocommerce-blocks/tree/trunk/docs/third-party-developers/extensibility), our development plan incorporates the following advanced features:

#### Advanced Store API Integration

**Store API Hooks Implementation**:
```php
// Enhanced Store API integration
class PayOp_Store_API_Integration {
    public function initialize() {
        // Hook into Store API payment processing (preferred method)
        add_action(
            'woocommerce_rest_checkout_process_payment_with_context',
            [$this, 'process_payment_with_context'],
            10,
            2
        );
        
        // Hook for order processing
        add_action(
            'woocommerce_store_api_checkout_order_processed',
            [$this, 'handle_order_processed']
        );
        
        // Register payment requirements filtering
        $this->register_payment_requirements();
    }
    
    public function process_payment_with_context($context, &$payment_result) {
        // PaymentContext provides: payment_method, order, payment_data
        if ($context->payment_method !== 'payop') {
            return;
        }
        
        try {
            // Extract PayOp-specific data from context
            $payment_method_id = $context->payment_data['payop_payment_method'] ?? null;
            $customer_data = $context->payment_data['payop_customer_data'] ?? [];
            
            // Process PayOp payment
            $result = $this->process_payop_payment($context->order, $payment_method_id, $customer_data);
            
            // Set PaymentResult status and details
            $payment_result->set_status($result['status']); // 'success', 'failure', 'pending', 'error'
            $payment_result->set_payment_details($result['details']);
            
            if ($result['redirect_url']) {
                $payment_result->set_redirect_url($result['redirect_url']);
            }
            
        } catch (Exception $e) {
            $payment_result->set_status('error');
            throw $e;
        }
    }
}
```

#### Enhanced Payment Method Filtering

**Dynamic Payment Method Availability**:
```js
// Enhanced canMakePayment implementation with Store API data
const canMakePayment = ({ 
    cart, 
    cartTotals, 
    cartNeedsShipping, 
    billingAddress, 
    shippingAddress, 
    selectedShippingMethods, 
    paymentRequirements 
}) => {
    // Access full cart context for advanced filtering
    const storeCurrency = cartTotals?.currency_code;
    const customerCountry = billingAddress?.country;
    const cartTotal = cartTotals?.total_price;
    
    // Filter PayOp methods by multiple criteria
    return paymentMethods.some(method => {
        // Currency filtering
        if (!method.currencies.includes(storeCurrency)) {
            return false;
        }
        
        // Country filtering
        if (method.countries.length && !method.countries.includes(customerCountry)) {
            return false;
        }
        
        // Amount filtering (if PayOp has limits)
        if (method.min_amount && cartTotal < method.min_amount) {
            return false;
        }
        
        if (method.max_amount && cartTotal > method.max_amount) {
            return false;
        }
        
        // Shipping requirements
        if (method.requires_shipping && !cartNeedsShipping) {
            return false;
        }
        
        return true;
    });
};
```

#### Advanced Event Handling

**Complete Event Lifecycle Management**:
```js
// Enhanced event registration following WooCommerce patterns
const PayOpPaymentMethod = ({ 
    eventRegistration, 
    emitResponse,
    billing,
    checkoutStatus,
    paymentStatus,
    // ... other props
}) => {
    const { 
        onPaymentSetup, 
        onCheckoutValidation, 
        onCheckoutSuccess, 
        onCheckoutFail 
    } = eventRegistration;
    
    // Payment setup event (replaces deprecated onPaymentProcessing)
    useEffect(() => {
        const unsubscribe = onPaymentSetup(async () => {
            // Validate payment data
            const validation = await validatePaymentData();
            
            if (!validation.isValid) {
                return {
                    type: emitResponse.responseTypes.ERROR,
                    message: validation.errorMessage,
                    messageContext: emitResponse.noticeContexts.PAYMENTS
                };
            }
            
            // Return payment data for server processing
            return {
                type: emitResponse.responseTypes.SUCCESS,
                meta: {
                    paymentMethodData: {
                        payop_payment_method: selectedMethod?.identifier,
                        payop_customer_data: customerData,
                        payop_validation_token: validation.token
                    },
                    billingAddress: validation.updatedBilling, // For express payments
                    shippingAddress: validation.updatedShipping // For express payments
                }
            };
        });
        
        return unsubscribe;
    }, [onPaymentSetup, emitResponse, selectedMethod, customerData]);
    
    // Checkout validation event
    useEffect(() => {
        const unsubscribe = onCheckoutValidation(() => {
            // Pre-checkout validation
            const errors = validateAllFields();
            
            if (errors.length > 0) {
                return {
                    errorMessage: 'Please fix the payment method errors',
                    validationErrors: errors
                };
            }
            
            return true;
        });
        
        return unsubscribe;
    }, [onCheckoutValidation]);
    
    // Success and failure handlers
    useEffect(() => {
        const unsubscribeSuccess = onCheckoutSuccess(({ paymentResult }) => {
            // Handle successful payment
            if (paymentResult.paymentStatus === 'success') {
                // Cleanup, analytics, etc.
                trackPaymentSuccess(paymentResult.paymentDetails);
            }
        });
        
        const unsubscribeFail = onCheckoutFail(() => {
            // Handle failed payment
            resetPaymentState();
            return true;
        });
        
        return () => {
            unsubscribeSuccess();
            unsubscribeFail();
        };
    }, [onCheckoutSuccess, onCheckoutFail]);
    
    // Component rendering...
};
```

#### Payment Method Extensions Callbacks

**Advanced Payment Method Filtering**:
```js
// Register advanced payment method filtering
import { registerPaymentMethodExtensionCallbacks } from '@woocommerce/blocks-registry';

registerPaymentMethodExtensionCallbacks('payop', {
    payop_bank_transfer: ({ cart, cartTotals, billingAddress, paymentRequirements }) => {
        // Custom logic for bank transfer availability
        return billingAddress.country === 'DE' && cartTotals.total_price >= 1000;
    },
    payop_cash_payment: ({ cart, cartTotals, billingAddress }) => {
        // Cash payments only for specific regions
        const cashPaymentCountries = ['PH', 'TH', 'VN', 'ID'];
        return cashPaymentCountries.includes(billingAddress.country);
    },
    payop_crypto: ({ cart, cartTotals }) => {
        // Crypto payments for orders above certain amount
        return cartTotals.total_price >= 5000;
    }
});
```

### Progressive Enhancement Strategy

#### Graceful Degradation Support

**Classic Checkout Fallback**:
```php
// Ensure compatibility with classic checkout
class PayOp_Classic_Checkout_Support {
    public function __construct() {
        // Only load if blocks are not available
        if (!$this->blocks_available()) {
            add_action('wp_enqueue_scripts', [$this, 'enqueue_classic_scripts']);
            add_action('woocommerce_checkout_process', [$this, 'process_classic_checkout']);
        }
    }
    
    private function blocks_available() {
        return class_exists('Automattic\WooCommerce\Blocks\Payments\Integrations\AbstractPaymentMethodType');
    }
}
```

### Development Timeline Updates

Based on the comprehensive WooCommerce Blocks documentation analysis, the timeline remains accurate at 12 weeks, but with enhanced deliverables:

**Phase 4 Enhanced Deliverables** (Block-Based Checkout Integration):
- ✅ Complete Store API integration with `woocommerce_rest_checkout_process_payment_with_context`
- ✅ Advanced event handling (`onPaymentSetup`, `onCheckoutValidation`, `onCheckoutSuccess`, `onCheckoutFail`)
- ✅ Payment method extension callbacks for dynamic filtering
- ✅ Progressive enhancement with classic checkout fallback
- ✅ Enhanced `PaymentResult` and `PaymentContext` handling
- ✅ Advanced `canMakePayment` logic with full cart context

This ensures our PayOp plugin will be fully compatible with the latest WooCommerce Blocks architecture and follows all documented best practices for extensibility and integration.
