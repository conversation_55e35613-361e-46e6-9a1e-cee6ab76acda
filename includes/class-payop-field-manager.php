<?php
/**
 * PayOp Field Manager - Handles dynamic payment method fields
 */

if (!defined('ABSPATH')) {
    exit;
}

class PayOp_Field_Manager {
    
    /**
     * Get required fields for a payment method
     */
    public function get_required_fields($method_identifier) {
        global $wpdb;
        
        $table = $wpdb->prefix . 'payop_payment_methods';
        $method = $wpdb->get_row(
            $wpdb->prepare("SELECT config FROM $table WHERE identifier = %d", $method_identifier),
            ARRAY_A
        );
        
        if (!$method) {
            return array();
        }
        
        $config = json_decode($method['config'], true);
        return $config['fields'] ?? array();
    }
    
    /**
     * Render fields HTML
     */
    public function render_fields($method_identifier, $values = array()) {
        $fields = $this->get_required_fields($method_identifier);
        
        if (empty($fields)) {
            return '';
        }
        
        ob_start();
        
        echo '<div class="payop-customer-fields">';
        echo '<h4>' . __('Additional Information Required', 'payop-personal-gateway') . '</h4>';
        
        foreach ($fields as $field) {
            $this->render_single_field($field, $values);
        }
        
        echo '</div>';
        
        return ob_get_clean();
    }
    
    /**
     * Render a single field
     */
    private function render_single_field($field, $values = array()) {
        $field_name = sanitize_key($field['name']);
        $field_value = $values[$field_name] ?? '';
        $field_id = 'payop_field_' . $field_name;
        $required = !empty($field['required']) ? 'required' : '';
        $placeholder = $field['placeholder'] ?? '';
        
        echo '<p class="form-row form-row-wide">';
        echo '<label for="' . esc_attr($field_id) . '">';
        echo esc_html($field['title'] ?? $field['name']);
        if (!empty($field['required'])) {
            echo ' <span class="required">*</span>';
        }
        echo '</label>';
        
        switch ($field['type']) {
            case 'email':
                echo '<input type="email" ';
                echo 'id="' . esc_attr($field_id) . '" ';
                echo 'name="payop_customer_data[' . esc_attr($field_name) . ']" ';
                echo 'value="' . esc_attr($field_value) . '" ';
                echo 'placeholder="' . esc_attr($placeholder) . '" ';
                echo 'class="input-text" ' . $required . ' />';
                break;
                
            case 'phone':
                echo '<input type="tel" ';
                echo 'id="' . esc_attr($field_id) . '" ';
                echo 'name="payop_customer_data[' . esc_attr($field_name) . ']" ';
                echo 'value="' . esc_attr($field_value) . '" ';
                echo 'placeholder="' . esc_attr($placeholder) . '" ';
                echo 'class="input-text" ' . $required . ' />';
                break;
                
            case 'date':
                echo '<input type="date" ';
                echo 'id="' . esc_attr($field_id) . '" ';
                echo 'name="payop_customer_data[' . esc_attr($field_name) . ']" ';
                echo 'value="' . esc_attr($field_value) . '" ';
                echo 'class="input-text" ' . $required . ' />';
                break;
                
            case 'select':
                echo '<select ';
                echo 'id="' . esc_attr($field_id) . '" ';
                echo 'name="payop_customer_data[' . esc_attr($field_name) . ']" ';
                echo 'class="select" ' . $required . '>';
                
                if (empty($field['required'])) {
                    echo '<option value="">' . __('Select...', 'payop-personal-gateway') . '</option>';
                }
                
                if (!empty($field['options'])) {
                    foreach ($field['options'] as $option) {
                        $selected = ($field_value === $option['value']) ? 'selected' : '';
                        echo '<option value="' . esc_attr($option['value']) . '" ' . $selected . '>';
                        echo esc_html($option['title']);
                        echo '</option>';
                    }
                }
                echo '</select>';
                break;
                
            case 'textarea':
                echo '<textarea ';
                echo 'id="' . esc_attr($field_id) . '" ';
                echo 'name="payop_customer_data[' . esc_attr($field_name) . ']" ';
                echo 'placeholder="' . esc_attr($placeholder) . '" ';
                echo 'class="input-text" rows="3" ' . $required . '>';
                echo esc_textarea($field_value);
                echo '</textarea>';
                break;
                
            default: // text
                echo '<input type="text" ';
                echo 'id="' . esc_attr($field_id) . '" ';
                echo 'name="payop_customer_data[' . esc_attr($field_name) . ']" ';
                echo 'value="' . esc_attr($field_value) . '" ';
                echo 'placeholder="' . esc_attr($placeholder) . '" ';
                echo 'class="input-text" ' . $required . ' />';
                break;
        }
        
        if (!empty($field['description'])) {
            echo '<span class="description">' . esc_html($field['description']) . '</span>';
        }
        
        echo '</p>';
    }
    
    /**
     * Validate field data
     */
    public function validate_fields($method_identifier, $data) {
        $fields = $this->get_required_fields($method_identifier);
        $errors = array();
        
        foreach ($fields as $field) {
            $field_name = $field['name'];
            $field_value = $data[$field_name] ?? '';
            
            // Check required fields
            if (!empty($field['required']) && empty($field_value)) {
                $errors[$field_name] = sprintf(
                    __('%s is required.', 'payop-personal-gateway'),
                    $field['title'] ?? $field_name
                );
                continue;
            }
            
            // Skip validation if field is empty and not required
            if (empty($field_value)) {
                continue;
            }
            
            // Validate field type
            $validation_error = $this->validate_field_type($field, $field_value);
            if ($validation_error) {
                $errors[$field_name] = $validation_error;
                continue;
            }
            
            // Validate with regex if provided
            if (!empty($field['regexp'])) {
                if (!preg_match('/' . $field['regexp'] . '/', $field_value)) {
                    $errors[$field_name] = sprintf(
                        __('Invalid format for %s.', 'payop-personal-gateway'),
                        $field['title'] ?? $field_name
                    );
                }
            }
            
            // Validate min/max length
            if (!empty($field['minLength']) && strlen($field_value) < $field['minLength']) {
                $errors[$field_name] = sprintf(
                    __('%s must be at least %d characters long.', 'payop-personal-gateway'),
                    $field['title'] ?? $field_name,
                    $field['minLength']
                );
            }
            
            if (!empty($field['maxLength']) && strlen($field_value) > $field['maxLength']) {
                $errors[$field_name] = sprintf(
                    __('%s must be no more than %d characters long.', 'payop-personal-gateway'),
                    $field['title'] ?? $field_name,
                    $field['maxLength']
                );
            }
        }
        
        return $errors;
    }
    
    /**
     * Validate field by type
     */
    private function validate_field_type($field, $value) {
        switch ($field['type']) {
            case 'email':
                if (!is_email($value)) {
                    return sprintf(
                        __('%s must be a valid email address.', 'payop-personal-gateway'),
                        $field['title'] ?? $field['name']
                    );
                }
                break;
                
            case 'phone':
                // Basic phone validation - only numbers, spaces, +, -, (, )
                if (!preg_match('/^[\d\s\+\-\(\)]+$/', $value)) {
                    return sprintf(
                        __('%s must be a valid phone number.', 'payop-personal-gateway'),
                        $field['title'] ?? $field['name']
                    );
                }
                break;
                
            case 'date':
                $date = DateTime::createFromFormat('Y-m-d', $value);
                if (!$date || $date->format('Y-m-d') !== $value) {
                    return sprintf(
                        __('%s must be a valid date.', 'payop-personal-gateway'),
                        $field['title'] ?? $field['name']
                    );
                }
                break;
                
            case 'select':
                if (!empty($field['options'])) {
                    $valid_values = array_column($field['options'], 'value');
                    if (!in_array($value, $valid_values)) {
                        return sprintf(
                            __('Please select a valid option for %s.', 'payop-personal-gateway'),
                            $field['title'] ?? $field['name']
                        );
                    }
                }
                break;
        }
        
        return null;
    }
    
    /**
     * Sanitize field data
     */
    public function sanitize_field_data($fields_config, $data) {
        $sanitized = array();
        
        foreach ($fields_config as $field) {
            $field_name = $field['name'];
            $field_value = $data[$field_name] ?? '';
            
            if (empty($field_value)) {
                continue;
            }
            
            switch ($field['type']) {
                case 'email':
                    $sanitized[$field_name] = sanitize_email($field_value);
                    break;
                    
                case 'phone':
                    $sanitized[$field_name] = preg_replace('/[^\d\s\+\-\(\)]/', '', $field_value);
                    break;
                    
                case 'date':
                    $sanitized[$field_name] = sanitize_text_field($field_value);
                    break;
                    
                case 'textarea':
                    $sanitized[$field_name] = sanitize_textarea_field($field_value);
                    break;
                    
                default:
                    $sanitized[$field_name] = sanitize_text_field($field_value);
                    break;
            }
        }
        
        return $sanitized;
    }
    
    /**
     * Get field types available
     */
    public function get_available_field_types() {
        return array(
            'text' => __('Text', 'payop-personal-gateway'),
            'email' => __('Email', 'payop-personal-gateway'),
            'phone' => __('Phone', 'payop-personal-gateway'),
            'date' => __('Date', 'payop-personal-gateway'),
            'select' => __('Select', 'payop-personal-gateway'),
            'textarea' => __('Textarea', 'payop-personal-gateway')
        );
    }
    
    /**
     * Get common field configurations
     */
    public function get_common_fields() {
        return array(
            'email' => array(
                'name' => 'email',
                'type' => 'email',
                'title' => __('Email Address', 'payop-personal-gateway'),
                'required' => true,
                'placeholder' => __('Enter your email address', 'payop-personal-gateway')
            ),
            'name' => array(
                'name' => 'name',
                'type' => 'text',
                'title' => __('Full Name', 'payop-personal-gateway'),
                'required' => true,
                'placeholder' => __('Enter your full name', 'payop-personal-gateway')
            ),
            'phone' => array(
                'name' => 'phone',
                'type' => 'phone',
                'title' => __('Phone Number', 'payop-personal-gateway'),
                'required' => false,
                'placeholder' => __('Enter your phone number', 'payop-personal-gateway')
            ),
            'document' => array(
                'name' => 'document',
                'type' => 'text',
                'title' => __('Document Number', 'payop-personal-gateway'),
                'required' => false,
                'placeholder' => __('Enter your document number', 'payop-personal-gateway'),
                'description' => __('ID, passport or other identification document', 'payop-personal-gateway')
            ),
            'date_of_birth' => array(
                'name' => 'date_of_birth',
                'type' => 'date',
                'title' => __('Date of Birth', 'payop-personal-gateway'),
                'required' => false
            )
        );
    }
}
