<?php
/**
 * PayOp WooCommerce Payment Gateway
 */

if (!defined('ABSPATH')) {
    exit;
}

class PayOp_WC_Gateway extends WC_Payment_Gateway {
    
    private $api_client;
    private $payment_methods_manager;
    private $field_manager;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->id = 'payop';
        $this->icon = '';
        $this->has_fields = true;
        $this->method_title = __('PayOp Personal Payment Gateway', 'payop-personal-gateway');
        $this->method_description = __('Accept payments via PayOp with 122+ payment methods', 'payop-personal-gateway');
        $this->supports = array(
            'products',
            'refunds'
        );
        
        // Load settings
        $this->init_form_fields();
        $this->init_settings();
        
        // Set gateway properties
        $this->title = $this->get_option('title');
        $this->description = $this->get_option('description');
        $this->enabled = $this->get_option('enabled');
        
        // Initialize components
        $this->api_client = new PayOp_API_Client(
            $this->get_option('public_key'),
            $this->get_option('secret_key'),
            $this->get_option('jwt_token'),
            $this->get_option('test_mode') === 'yes'
        );
        
        $this->payment_methods_manager = new PayOp_Payment_Methods($this->api_client);
        $this->field_manager = new PayOp_Field_Manager();
        
        // Hooks
        add_action('woocommerce_update_options_payment_gateways_' . $this->id, array($this, 'process_admin_options'));
        add_action('wp_enqueue_scripts', array($this, 'payment_scripts'));
        add_action('woocommerce_api_payop_ipn', array($this, 'handle_ipn'));
        add_action('woocommerce_thankyou_' . $this->id, array($this, 'thankyou_page'));
        
        // AJAX handlers
        add_action('wp_ajax_payop_get_payment_methods', array($this, 'ajax_get_payment_methods'));
        add_action('wp_ajax_nopriv_payop_get_payment_methods', array($this, 'ajax_get_payment_methods'));
        add_action('wp_ajax_payop_get_payment_fields', array($this, 'ajax_get_payment_fields'));
        add_action('wp_ajax_nopriv_payop_get_payment_fields', array($this, 'ajax_get_payment_fields'));
    }
    
    /**
     * Initialize gateway form fields
     */
    public function init_form_fields() {
        $this->form_fields = array(
            'enabled' => array(
                'title' => __('Enable/Disable', 'payop-personal-gateway'),
                'type' => 'checkbox',
                'label' => __('Enable PayOp Personal Payment Gateway', 'payop-personal-gateway'),
                'default' => 'no'
            ),
            'title' => array(
                'title' => __('Title', 'payop-personal-gateway'),
                'type' => 'text',
                'description' => __('This controls the title shown during checkout.', 'payop-personal-gateway'),
                'default' => __('PayOp Personal Payment Gateway', 'payop-personal-gateway'),
                'desc_tip' => true,
            ),
            'description' => array(
                'title' => __('Description', 'payop-personal-gateway'),
                'type' => 'textarea',
                'description' => __('Payment method description that the customer will see on your checkout.', 'payop-personal-gateway'),
                'default' => __('Pay securely using PayOp with multiple payment options.', 'payop-personal-gateway'),
                'desc_tip' => true,
            ),
            'test_mode' => array(
                'title' => __('Test Mode', 'payop-personal-gateway'),
                'type' => 'checkbox',
                'label' => __('Enable Test Mode', 'payop-personal-gateway'),
                'default' => 'yes',
                'description' => __('Use PayOp sandbox for testing.', 'payop-personal-gateway'),
            ),
            'public_key' => array(
                'title' => __('Public Key', 'payop-personal-gateway'),
                'type' => 'text',
                'description' => __('Get this from your PayOp account.', 'payop-personal-gateway'),
                'default' => '',
                'desc_tip' => true,
            ),
            'secret_key' => array(
                'title' => __('Secret Key', 'payop-personal-gateway'),
                'type' => 'password',
                'description' => __('Get this from your PayOp account.', 'payop-personal-gateway'),
                'default' => '',
                'desc_tip' => true,
            ),
            'jwt_token' => array(
                'title' => __('JWT Token', 'payop-personal-gateway'),
                'type' => 'textarea',
                'description' => __('JWT token for API authentication.', 'payop-personal-gateway'),
                'default' => '',
                'desc_tip' => true,
            ),
            'payment_method_grouping' => array(
                'title' => __('Payment Method Grouping', 'payop-personal-gateway'),
                'type' => 'select',
                'description' => __('How to group payment methods for customers.', 'payop-personal-gateway'),
                'default' => 'type',
                'options' => array(
                    'none' => __('No Grouping', 'payop-personal-gateway'),
                    'type' => __('Group by Type', 'payop-personal-gateway'),
                    'country' => __('Group by Country', 'payop-personal-gateway'),
                    'currency' => __('Group by Currency', 'payop-personal-gateway')
                ),
                'desc_tip' => true,
            )
        );
    }
    
    /**
     * Check if gateway is available
     */
    public function is_available() {
        if ($this->enabled !== 'yes') {
            return false;
        }
        
        if (empty($this->get_option('public_key')) || empty($this->get_option('secret_key'))) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Payment form on checkout page
     */
    public function payment_fields() {
        if ($this->description) {
            echo wpautop(wp_kses_post($this->description));
        }
        
        // Get available payment methods
        $currency = get_woocommerce_currency();
        $country = WC()->customer ? WC()->customer->get_billing_country() : '';
        
        try {
            $payment_methods = $this->payment_methods_manager->get_available_methods($currency, $country);
            
            if (!empty($payment_methods)) {
                echo '<div class="payop-payment-method-selector">';
                echo '<label for="payop_payment_method">' . __('Select Payment Method:', 'payop-personal-gateway') . '</label>';
                echo '<select id="payop_payment_method" name="payop_payment_method" required>';
                echo '<option value="">' . __('Choose payment method...', 'payop-personal-gateway') . '</option>';
                
                foreach ($payment_methods as $method) {
                    if ($method['is_enabled']) {
                        printf(
                            '<option value="%s">%s</option>',
                            esc_attr($method['identifier']),
                            esc_html($method['title'])
                        );
                    }
                }
                
                echo '</select>';
                echo '</div>';
                
                echo '<div id="payop-dynamic-fields"></div>';
            } else {
                echo '<p>' . __('No payment methods available for your location.', 'payop-personal-gateway') . '</p>';
            }
        } catch (Exception $e) {
            echo '<p>' . __('Unable to load payment methods. Please try again.', 'payop-personal-gateway') . '</p>';
        }
    }
    
    /**
     * Render payment methods
     */
    private function render_payment_methods($methods, $grouping) {
        $grouped_methods = $this->group_methods($methods, $grouping);
        
        foreach ($grouped_methods as $group_name => $group_methods) {
            if ($grouping !== 'none') {
                echo '<h4>' . esc_html($this->get_group_title($group_name)) . '</h4>';
            }
            
            echo '<div class="payop-method-group">';
            foreach ($group_methods as $method) {
                $this->render_single_method($method);
            }
            echo '</div>';
        }
    }
    
    /**
     * Render single payment method
     */
    private function render_single_method($method) {
        $method_id = esc_attr($method['identifier']);
        $method_title = esc_html($method['title']);
        $method_logo = esc_url($method['logo'] ?? '');
        
        echo '<div class="payop-payment-method">';
        echo '<label for="payop_method_' . $method_id . '">';
        echo '<input type="radio" id="payop_method_' . $method_id . '" name="payop_payment_method" value="' . $method_id . '" />';
        
        if ($method_logo) {
            echo '<img src="' . $method_logo . '" alt="' . $method_title . '" class="payop-method-logo" />';
        }
        
        echo '<span class="payop-method-title">' . $method_title . '</span>';
        echo '</label>';
        echo '</div>';
    }
    
    /**
     * Group payment methods
     */
    private function group_methods($methods, $grouping) {
        if ($grouping === 'none') {
            return array('all' => $methods);
        }
        
        $grouped = array();
        foreach ($methods as $method) {
            $key = $this->get_grouping_key($method, $grouping);
            if (!isset($grouped[$key])) {
                $grouped[$key] = array();
            }
            $grouped[$key][] = $method;
        }
        
        return $grouped;
    }
    
    /**
     * Get grouping key for method
     */
    private function get_grouping_key($method, $grouping) {
        switch ($grouping) {
            case 'type':
                return $method['type'] ?? 'other';
            case 'country':
                return $method['countries'][0] ?? 'global';
            default:
                return 'all';
        }
    }
    
    /**
     * Get group title
     */
    private function get_group_title($group_key) {
        $titles = array(
            'bank_transfer' => __('Bank Transfers', 'payop-personal-gateway'),
            'cash' => __('Cash Payments', 'payop-personal-gateway'),
            'ewallet' => __('E-Wallets', 'payop-personal-gateway'),
            'cards_international' => __('Credit/Debit Cards', 'payop-personal-gateway'),
            'crypto' => __('Cryptocurrency', 'payop-personal-gateway'),
        );
        
        return $titles[$group_key] ?? ucfirst(str_replace('_', ' ', $group_key));
    }
    
    /**
     * Process payment
     */
    public function process_payment($order_id) {
        $order = wc_get_order($order_id);
        
        if (!$order) {
            throw new Exception(__('Order not found.', 'payop-personal-gateway'));
        }
        
        // Get posted data
        $payment_method_id = isset($_POST['payop_payment_method']) ? sanitize_text_field($_POST['payop_payment_method']) : '';
        $customer_data = isset($_POST['payop_customer_data']) ? array_map('sanitize_text_field', $_POST['payop_customer_data']) : array();
        
        if (empty($payment_method_id)) {
            wc_add_notice(__('Please select a payment method.', 'payop-personal-gateway'), 'error');
            return;
        }
        
        try {
            // Create PayOp invoice
            $invoice_data = $this->prepare_invoice_data($order, $payment_method_id, $customer_data);
            $invoice_response = $this->api_client->create_invoice($invoice_data);
            
            if (!$invoice_response || $invoice_response['status'] !== 'success') {
                throw new Exception($invoice_response['message'] ?? __('Failed to create invoice.', 'payop-personal-gateway'));
            }
            
            $invoice = $invoice_response['data'];
            
            // Create checkout transaction
            $checkout_data = $this->prepare_checkout_data($invoice, $customer_data, $payment_method_id);
            $checkout_response = $this->api_client->create_checkout($checkout_data);
            
            if (!$checkout_response || $checkout_response['status'] !== 'success') {
                throw new Exception($checkout_response['message'] ?? __('Failed to create checkout.', 'payop-personal-gateway'));
            }
            
            $checkout = $checkout_response['data'];
            
            // Store transaction data
            $this->store_transaction_data($order, $invoice, $checkout, $customer_data);
            
            // Update order status
            $order->update_status('pending', __('Awaiting PayOp payment', 'payop-personal-gateway'));
            
            // Reduce stock
            wc_reduce_stock_levels($order_id);
            
            // Empty cart
            WC()->cart->empty_cart();
            
            // Return redirect
            return array(
                'result' => 'success',
                'redirect' => $checkout['directUrl'] ?? $this->get_return_url($order)
            );
            
        } catch (Exception $e) {
            wc_add_notice(__('Payment error: ', 'payop-personal-gateway') . $e->getMessage(), 'error');
            return;
        }
    }
    
    /**
     * Prepare invoice data for PayOp
     */
    private function prepare_invoice_data($order, $payment_method_id, $customer_data) {
        return array(
            'publicKey' => $this->get_option('public_key'),
            'order' => array(
                'number' => $order->get_order_number(),
                'amount' => $order->get_total(),
                'currency' => $order->get_currency(),
                'description' => sprintf(__('Order #%s', 'payop-personal-gateway'), $order->get_order_number()),
                'items' => $this->get_order_items($order)
            ),
            'payer' => array(
                'email' => $order->get_billing_email(),
                'name' => trim($order->get_billing_first_name() . ' ' . $order->get_billing_last_name()),
                'phone' => $order->get_billing_phone()
            ),
            'paymentMethod' => $payment_method_id,
            'resultUrl' => $this->get_return_url($order),
            'failPath' => $order->get_cancel_order_url(),
            'notificationUrl' => WC()->api_request_url('payop_ipn')
        );
    }
    
    /**
     * Prepare checkout data
     */
    private function prepare_checkout_data($invoice, $customer_data, $payment_method_id) {
        return array(
            'invoiceIdentifier' => $invoice['id'],
            'customer' => array_merge(
                array(
                    'email' => $invoice['payer']['email'],
                    'name' => $invoice['payer']['name'],
                    'phone' => $invoice['payer']['phone']
                ),
                $customer_data
            ),
            'paymentMethod' => $payment_method_id
        );
    }
    
    /**
     * Get order items for PayOp
     */
    private function get_order_items($order) {
        $items = array();
        
        foreach ($order->get_items() as $item) {
            $items[] = array(
                'name' => $item->get_name(),
                'quantity' => $item->get_quantity(),
                'price' => $item->get_total()
            );
        }
        
        return $items;
    }
    
    /**
     * Store transaction data
     */
    private function store_transaction_data($order, $invoice, $checkout, $customer_data) {
        global $wpdb;
        
        $table = $wpdb->prefix . 'payop_transactions';
        
        $wpdb->insert(
            $table,
            array(
                'order_id' => $order->get_id(),
                'invoice_id' => $invoice['id'],
                'transaction_id' => $checkout['transactionIdentifier'] ?? '',
                'payment_method_id' => $checkout['paymentMethod'] ?? '',
                'amount' => $order->get_total(),
                'currency' => $order->get_currency(),
                'status' => 'pending',
                'customer_data' => json_encode($customer_data),
                'api_response' => json_encode(array('invoice' => $invoice, 'checkout' => $checkout))
            ),
            array('%d', '%s', '%s', '%s', '%f', '%s', '%s', '%s', '%s')
        );
        
        // Store in order meta
        $order->update_meta_data('_payop_invoice_id', $invoice['id']);
        $order->update_meta_data('_payop_transaction_id', $checkout['transactionIdentifier'] ?? '');
        $order->save();
    }
    
    /**
     * Handle IPN notifications
     */
    public function handle_ipn() {
        $ipn_handler = new PayOp_IPN_Handler($this->api_client);
        $ipn_handler->handle();
    }
    
    /**
     * Thank you page
     */
    public function thankyou_page($order_id) {
        $order = wc_get_order($order_id);
        
        if ($order && $order->get_payment_method() === $this->id) {
            echo '<h2>' . __('Payment Information', 'payop-personal-gateway') . '</h2>';
            echo '<p>' . __('Your payment is being processed. You will receive a confirmation email once the payment is complete.', 'payop-personal-gateway') . '</p>';
        }
    }
    
    /**
     * Load payment scripts
     */
    public function payment_scripts() {
        if (!is_checkout() || !$this->is_available()) {
            return;
        }
        
        wp_enqueue_script(
            'payop-payment',
            PAYOP_WC_PLUGIN_URL . 'assets/js/payop-payment.js',
            array('jquery'),
            PAYOP_WC_VERSION,
            true
        );
        
        wp_localize_script('payop-payment', 'payop_params', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('payop_nonce'),
            'currency' => get_woocommerce_currency(),
            'country' => WC()->customer ? WC()->customer->get_billing_country() : '',
            'messages' => array(
                'select_method' => __('Please select a payment method.', 'payop-personal-gateway'),
                'loading' => __('Loading...', 'payop-personal-gateway')
            )
        ));
        
        wp_enqueue_style(
            'payop-payment',
            PAYOP_WC_PLUGIN_URL . 'assets/css/payop-payment.css',
            array(),
            PAYOP_WC_VERSION
        );
    }
    
    /**
     * AJAX: Get payment methods
     */
    public function ajax_get_payment_methods() {
        check_ajax_referer('payop_payment_nonce', 'nonce');
        
        $currency = get_woocommerce_currency();
        $country = WC()->customer ? WC()->customer->get_billing_country() : '';
        
        try {
            $methods = $this->payment_methods_manager->get_available_methods($currency, $country);
            wp_send_json_success(array('methods' => $methods));
        } catch (Exception $e) {
            wp_send_json_error(array('message' => $e->getMessage()));
        }
    }
    
    /**
     * AJAX: Get payment fields for selected method
     */
    public function ajax_get_payment_fields() {
        check_ajax_referer('payop_payment_nonce', 'nonce');
        
        $payment_method = sanitize_text_field($_POST['payment_method'] ?? '');
        
        if (empty($payment_method)) {
            wp_send_json_error(array('message' => 'Payment method is required'));
        }
        
        try {
            $method_data = $this->payment_methods_manager->get_method_by_identifier($payment_method);
            
            if (!$method_data) {
                wp_send_json_error(array('message' => 'Payment method not found'));
            }
            
            $fields_html = $this->field_manager->render_fields($method_data);
            
            wp_send_json_success(array(
                'html' => $fields_html,
                'method' => $method_data
            ));
            
        } catch (Exception $e) {
            wp_send_json_error(array('message' => $e->getMessage()));
        }
    }
}
