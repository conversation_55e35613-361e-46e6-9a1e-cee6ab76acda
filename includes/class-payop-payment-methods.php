<?php
/**
 * PayOp Payment Methods Manager
 */

if (!defined('ABSPATH')) {
    exit;
}

class PayOp_Payment_Methods {
    
    private $api_client;
    private $cache_duration = HOUR_IN_SECONDS;
    
    /**
     * Constructor
     */
    public function __construct($api_client) {
        $this->api_client = $api_client;
    }
    
    /**
     * Get available payment methods
     */
    public function get_available_methods($currency = '', $country = '') {
        $cache_key = 'payop_methods_' . md5($currency . $country . $this->api_client->get_option('public_key'));
        $methods = get_transient($cache_key);
        
        if (false === $methods) {
            $methods = $this->fetch_and_cache_methods();
            set_transient($cache_key, $methods, $this->cache_duration);
        }
        
        // Filter by currency and country
        return $this->filter_methods($methods, $currency, $country);
    }
    
    /**
     * Fetch payment methods from API and cache them
     */
    private function fetch_and_cache_methods() {
        try {
            $response = $this->api_client->get_payment_methods();
            
            if (!$response || !isset($response['data'])) {
                throw new Exception('Invalid API response');
            }
            
            $methods = $response['data'];
            
            // Store in database for backup
            $this->store_methods_in_db($methods);
            
            return $methods;
            
        } catch (Exception $e) {
            // Fallback to database cache
            return $this->get_methods_from_db();
        }
    }
    
    /**
     * Store methods in database
     */
    private function store_methods_in_db($methods) {
        global $wpdb;
        
        $table = $wpdb->prefix . 'payop_payment_methods';
        
        // Clear existing methods
        $wpdb->query("DELETE FROM $table");
        
        foreach ($methods as $method) {
            $wpdb->insert(
                $table,
                array(
                    'identifier' => $method['identifier'],
                    'type' => $method['type'] ?? '',
                    'title' => $method['title'] ?? '',
                    'logo' => $method['logo'] ?? '',
                    'currencies' => json_encode($method['currencies'] ?? array()),
                    'countries' => json_encode($method['countries'] ?? array()),
                    'config' => json_encode($method['config'] ?? array()),
                    'is_enabled' => 1,
                    'display_order' => 0
                ),
                array('%d', '%s', '%s', '%s', '%s', '%s', '%s', '%d', '%d')
            );
        }
        
        update_option('payop_last_method_sync', time());
    }
    
    /**
     * Get methods from database
     */
    private function get_methods_from_db() {
        global $wpdb;
        
        $table = $wpdb->prefix . 'payop_payment_methods';
        $results = $wpdb->get_results("SELECT * FROM $table WHERE is_enabled = 1 ORDER BY display_order ASC", ARRAY_A);
        
        $methods = array();
        foreach ($results as $row) {
            $methods[] = array(
                'identifier' => (int) $row['identifier'],
                'type' => $row['type'],
                'title' => $row['title'],
                'logo' => $row['logo'],
                'currencies' => json_decode($row['currencies'], true) ?: array(),
                'countries' => json_decode($row['countries'], true) ?: array(),
                'config' => json_decode($row['config'], true) ?: array()
            );
        }
        
        return $methods;
    }
    
    /**
     * Filter methods by currency and country
     */
    private function filter_methods($methods, $currency, $country) {
        if (empty($methods)) {
            return array();
        }
        
        $filtered = array();
        
        foreach ($methods as $method) {
            // Check currency
            if (!empty($currency) && !empty($method['currencies'])) {
                if (!in_array($currency, $method['currencies'])) {
                    continue;
                }
            }
            
            // Check country
            if (!empty($country) && !empty($method['countries'])) {
                if (!in_array($country, $method['countries'])) {
                    continue;
                }
            }
            
            $filtered[] = $method;
        }
        
        return $filtered;
    }
    
    /**
     * Get method by identifier
     */
    public function get_method_by_identifier($identifier) {
        global $wpdb;
        
        $table = $wpdb->prefix . 'payop_payment_methods';
        $result = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM $table WHERE identifier = %d", $identifier),
            ARRAY_A
        );
        
        if (!$result) {
            return null;
        }
        
        return array(
            'identifier' => (int) $result['identifier'],
            'type' => $result['type'],
            'title' => $result['title'],
            'logo' => $result['logo'],
            'currencies' => json_decode($result['currencies'], true) ?: array(),
            'countries' => json_decode($result['countries'], true) ?: array(),
            'config' => json_decode($result['config'], true) ?: array()
        );
    }
    
    /**
     * Refresh payment methods cache
     */
    public function refresh_methods() {
        global $wpdb;
        
        // Clear transients
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_payop_methods_%'");
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_payop_methods_%'");
        
        // Fetch fresh data
        return $this->fetch_and_cache_methods();
    }
    
    /**
     * Enable/disable payment method
     */
    public function toggle_method($identifier, $enabled) {
        global $wpdb;
        
        $table = $wpdb->prefix . 'payop_payment_methods';
        
        return $wpdb->update(
            $table,
            array('is_enabled' => $enabled ? 1 : 0),
            array('identifier' => $identifier),
            array('%d'),
            array('%d')
        );
    }
    
    /**
     * Update method display order
     */
    public function update_method_order($identifier, $order) {
        global $wpdb;
        
        $table = $wpdb->prefix . 'payop_payment_methods';
        
        return $wpdb->update(
            $table,
            array('display_order' => $order),
            array('identifier' => $identifier),
            array('%d'),
            array('%d')
        );
    }
    
    /**
     * Get payment method statistics
     */
    public function get_method_stats() {
        global $wpdb;
        
        $methods_table = $wpdb->prefix . 'payop_payment_methods';
        $transactions_table = $wpdb->prefix . 'payop_transactions';
        
        $stats = array();
        
        // Total methods
        $stats['total_methods'] = $wpdb->get_var("SELECT COUNT(*) FROM $methods_table");
        $stats['enabled_methods'] = $wpdb->get_var("SELECT COUNT(*) FROM $methods_table WHERE is_enabled = 1");
        
        // Methods by type
        $type_stats = $wpdb->get_results("
            SELECT type, COUNT(*) as count 
            FROM $methods_table 
            WHERE is_enabled = 1 
            GROUP BY type
        ", ARRAY_A);
        
        $stats['by_type'] = array();
        foreach ($type_stats as $stat) {
            $stats['by_type'][$stat['type']] = (int) $stat['count'];
        }
        
        // Transaction stats
        $stats['total_transactions'] = $wpdb->get_var("SELECT COUNT(*) FROM $transactions_table");
        $stats['successful_transactions'] = $wpdb->get_var("SELECT COUNT(*) FROM $transactions_table WHERE status = 'completed'");
        
        // Last sync time
        $stats['last_sync'] = get_option('payop_last_method_sync', 0);
        
        return $stats;
    }
    
    /**
     * Get supported currencies
     */
    public function get_supported_currencies() {
        global $wpdb;
        
        $table = $wpdb->prefix . 'payop_payment_methods';
        $results = $wpdb->get_results("SELECT currencies FROM $table WHERE is_enabled = 1", ARRAY_A);
        
        $currencies = array();
        foreach ($results as $row) {
            $method_currencies = json_decode($row['currencies'], true);
            if (is_array($method_currencies)) {
                $currencies = array_merge($currencies, $method_currencies);
            }
        }
        
        return array_unique($currencies);
    }
    
    /**
     * Get supported countries
     */
    public function get_supported_countries() {
        global $wpdb;
        
        $table = $wpdb->prefix . 'payop_payment_methods';
        $results = $wpdb->get_results("SELECT countries FROM $table WHERE is_enabled = 1", ARRAY_A);
        
        $countries = array();
        foreach ($results as $row) {
            $method_countries = json_decode($row['countries'], true);
            if (is_array($method_countries)) {
                $countries = array_merge($countries, $method_countries);
            }
        }
        
        return array_unique($countries);
    }
}
