<?php
/**
 * PayOp IPN (Instant Payment Notification) Handler
 */

if (!defined('ABSPATH')) {
    exit;
}

class PayOp_IPN_Handler {
    
    private $api_client;
    
    /**
     * Constructor
     */
    public function __construct($api_client) {
        $this->api_client = $api_client;
    }
    
    /**
     * Handle IPN request
     */
    public function handle() {
        // Validate request method
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->log('Invalid request method: ' . $_SERVER['REQUEST_METHOD']);
            http_response_code(405);
            exit('Method not allowed');
        }
        
        // Validate IP address
        $remote_ip = $this->get_remote_ip();
        if (!$this->api_client->validate_ipn_ip($remote_ip)) {
            $this->log('Invalid IP address: ' . $remote_ip);
            http_response_code(403);
            exit('Forbidden');
        }
        
        // Get and validate payload
        $raw_body = file_get_contents('php://input');
        if (empty($raw_body)) {
            $this->log('Empty request body');
            http_response_code(400);
            exit('Bad request');
        }
        
        $payload = json_decode($raw_body, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->log('Invalid JSON payload: ' . json_last_error_msg());
            http_response_code(400);
            exit('Invalid JSON');
        }
        
        $this->log('IPN received', $payload);
        
        try {
            $this->process_ipn($payload);
            http_response_code(200);
            echo 'OK';
        } catch (Exception $e) {
            $this->log('IPN processing error: ' . $e->getMessage());
            http_response_code(500);
            echo 'Error: ' . $e->getMessage();
        }
        
        exit;
    }
    
    /**
     * Process IPN data
     */
    private function process_ipn($payload) {
        // Validate required fields
        if (!isset($payload['invoice']) || !isset($payload['transaction'])) {
            throw new Exception('Missing required fields in IPN payload');
        }
        
        $invoice = $payload['invoice'];
        $transaction = $payload['transaction'];
        
        $invoice_id = $invoice['id'] ?? '';
        $transaction_id = $transaction['id'] ?? '';
        $status = $invoice['status'] ?? 0;
        $order_number = $transaction['order']['id'] ?? '';
        
        if (empty($invoice_id) || empty($order_number)) {
            throw new Exception('Missing invoice ID or order number');
        }
        
        // Find the order
        $order = $this->find_order_by_number($order_number);
        if (!$order) {
            throw new Exception('Order not found: ' . $order_number);
        }
        
        // Verify this IPN hasn't been processed already
        if ($this->is_ipn_already_processed($order->get_id(), $transaction_id)) {
            $this->log('IPN already processed for order ' . $order->get_id());
            return;
        }
        
        // Update order based on status
        $this->update_order_status($order, $status, $payload);
        
        // Log transaction update
        $this->log_transaction_update($order->get_id(), $payload);
        
        // Store IPN data in order meta
        $order->update_meta_data('_payop_ipn_data', $payload);
        $order->update_meta_data('_payop_ipn_processed', time());
        $order->save();
    }
    
    /**
     * Find order by order number
     */
    private function find_order_by_number($order_number) {
        // First try to find by order ID
        $order = wc_get_order($order_number);
        if ($order) {
            return $order;
        }
        
        // Try to find by order number (in case of custom order numbers)
        $orders = wc_get_orders(array(
            'limit' => 1,
            'meta_query' => array(
                array(
                    'key' => '_order_number',
                    'value' => $order_number,
                    'compare' => '='
                )
            )
        ));
        
        if (!empty($orders)) {
            return $orders[0];
        }
        
        // Try to find by PayOp invoice ID
        $orders = wc_get_orders(array(
            'limit' => 1,
            'meta_query' => array(
                array(
                    'key' => '_payop_invoice_id',
                    'value' => $order_number,
                    'compare' => '='
                )
            )
        ));
        
        return !empty($orders) ? $orders[0] : null;
    }
    
    /**
     * Check if IPN has already been processed
     */
    private function is_ipn_already_processed($order_id, $transaction_id) {
        global $wpdb;
        
        $table = $wpdb->prefix . 'payop_transactions';
        $result = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM $table WHERE order_id = %d AND transaction_id = %s AND status != 'pending'",
                $order_id,
                $transaction_id
            )
        );
        
        return $result > 0;
    }
    
    /**
     * Update order status based on PayOp status
     */
    private function update_order_status($order, $status, $payload) {
        $old_status = $order->get_status();
        $transaction_id = $payload['transaction']['id'] ?? '';
        
        switch ($status) {
            case 1: // Paid
                if (!$order->is_paid()) {
                    $order->payment_complete($transaction_id);
                    $order->add_order_note(
                        sprintf(__('Payment completed via PayOp. Transaction ID: %s', 'payop-personal-gateway'), $transaction_id)
                    );
                    $this->update_transaction_status($order->get_id(), 'completed');
                }
                break;
                
            case 2: // Overdue/Expired
                if ($old_status === 'pending') {
                    $order->update_status('cancelled', __('Payment expired via PayOp', 'payop-personal-gateway'));
                    $this->update_transaction_status($order->get_id(), 'expired');
                }
                break;
                
            case 5: // Failed
                if ($old_status === 'pending') {
                    $order->update_status('failed', __('Payment failed via PayOp', 'payop-personal-gateway'));
                    $this->update_transaction_status($order->get_id(), 'failed');
                }
                break;
                
            case 6: // Refunded
                if ($order->is_paid()) {
                    $order->update_status('refunded', __('Payment refunded via PayOp', 'payop-personal-gateway'));
                    $this->update_transaction_status($order->get_id(), 'refunded');
                }
                break;
                
            default:
                $this->log('Unknown PayOp status: ' . $status);
                break;
        }
    }
    
    /**
     * Update transaction status in database
     */
    private function update_transaction_status($order_id, $status) {
        global $wpdb;
        
        $table = $wpdb->prefix . 'payop_transactions';
        
        $wpdb->update(
            $table,
            array(
                'status' => $status,
                'updated_at' => current_time('mysql')
            ),
            array('order_id' => $order_id),
            array('%s', '%s'),
            array('%d')
        );
    }
    
    /**
     * Log transaction update
     */
    private function log_transaction_update($order_id, $payload) {
        global $wpdb;
        
        $table = $wpdb->prefix . 'payop_transactions';
        
        // Update existing transaction record
        $wpdb->update(
            $table,
            array(
                'api_response' => json_encode($payload),
                'updated_at' => current_time('mysql')
            ),
            array('order_id' => $order_id),
            array('%s', '%s'),
            array('%d')
        );
    }
    
    /**
     * Get remote IP address
     */
    private function get_remote_ip() {
        // Check for shared internet/proxy
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            return $_SERVER['HTTP_CLIENT_IP'];
        }
        // Check for IP from remote share via proxy
        elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            return $_SERVER['HTTP_X_FORWARDED_FOR'];
        }
        // Check for IP from remote address
        else {
            return $_SERVER['REMOTE_ADDR'];
        }
    }
    
    /**
     * Log IPN activity
     */
    private function log($message, $data = null) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('[PayOp IPN] ' . $message);
            if ($data) {
                error_log('[PayOp IPN Data] ' . print_r($data, true));
            }
        }
        
        // Also log to WooCommerce logger if available
        if (function_exists('wc_get_logger')) {
            $logger = wc_get_logger();
            $context = array('source' => 'payop-ipn');
            if ($data) {
                $context['data'] = $data;
            }
            $logger->info($message, $context);
        }
    }
    
    /**
     * Send test IPN (for debugging)
     */
    public function send_test_ipn($order_id) {
        $order = wc_get_order($order_id);
        if (!$order) {
            return false;
        }
        
        $test_payload = array(
            'invoice' => array(
                'id' => $order->get_meta('_payop_invoice_id'),
                'status' => 1 // Paid
            ),
            'transaction' => array(
                'id' => $order->get_meta('_payop_transaction_id') ?: 'test_' . time(),
                'order' => array(
                    'id' => $order->get_order_number()
                )
            )
        );
        
        $this->log('Sending test IPN', $test_payload);
        
        try {
            $this->process_ipn($test_payload);
            return true;
        } catch (Exception $e) {
            $this->log('Test IPN failed: ' . $e->getMessage());
            return false;
        }
    }
}
