# PayOp Personal Payment Gateway

A simplified WooCommerce payment gateway plugin for PayOp with 122+ payment methods support. This is a personal-use version that won't conflict with official PayOp plugins.

## Installation

1. Copy the `payop-woo` folder to your WordPress `wp-content/plugins/` directory
2. Activate the plugin through the WordPress admin panel
3. Configure the gateway settings in WooCommerce > Settings > Payments > PayOp

## Configuration

### API Credentials
You'll need the following from your PayOp account:
- **Public Key**: Your PayOp public key
- **Secret Key**: Your PayOp secret key  
- **JWT Token**: Your PayOp JWT token

### Basic Setup
1. Go to **WooCommerce > Settings > Payments**
2. Find **PayOp Personal Payment Gateway** and click **Set up**
3. Enable the gateway and enter your API credentials
4. Configure the payment settings as needed
5. Save changes

### Payment Methods Management
1. Go to **WooCommerce > PayOp Methods** to manage payment methods
2. Click **Sync Payment Methods** to fetch available methods from PayOp
3. Enable/disable individual payment methods as needed

## Features

- **122+ Payment Methods**: Support for all PayOp payment methods
- **Dynamic Fields**: Automatically renders required fields based on selected payment method
- **Admin Interface**: Easy management of payment methods
- **AJAX-powered**: Smooth user experience with dynamic loading
- **Mobile Responsive**: Works on all devices
- **Simple Setup**: No complex build tools or dependencies

## File Structure

```
payop-woo/
├── payop-woocommerce.php          # Main plugin file
├── includes/
│   ├── class-payop-api-client.php  # API communication
│   ├── class-payop-gateway.php     # WooCommerce gateway
│   ├── class-payop-payment-methods.php # Payment methods management
│   ├── class-payop-field-manager.php   # Dynamic field handling
│   ├── class-payop-ipn-handler.php     # IPN/webhook processing
│   └── admin/
│       └── class-payop-admin.php    # Admin interface
├── assets/
│   ├── js/
│   │   ├── payop-payment.js         # Frontend JavaScript
│   │   └── payop-admin.js           # Admin JavaScript
│   └── css/
│       └── payop-payment.css        # Styles
└── README.md                        # This file
```

## Usage

### Customer Experience
1. Customer adds products to cart and proceeds to checkout
2. Selects "PayOp Personal Payment Gateway" as payment method
3. Chooses specific payment method from dropdown
4. Fills in required fields (dynamically loaded)
5. Completes payment through PayOp's secure redirect

### Admin Experience  
1. View all available payment methods in **WooCommerce > PayOp Methods**
2. Enable/disable payment methods as needed
3. Sync new methods from PayOp API
4. Test API connection to verify credentials
5. Monitor transaction logs and status

## Troubleshooting

### Common Issues

**Plugin activation error about missing blocks file**
- This has been fixed - the plugin now works with classic WooCommerce checkout
- WooCommerce Blocks support was removed to keep the implementation simple
- HPOS (High-Performance Order Storage) compatibility has been added

**Plugin update notifications for other PayOp plugins**
- This plugin is named "PayOp Personal Payment Gateway" to avoid conflicts
- It won't be overwritten by official PayOp plugin updates
- Use this version for personal/custom implementations

**"No payment methods available"**
- Check API credentials are correct
- Ensure currency and country are supported
- Try syncing payment methods manually

**"Unable to load payment methods"**
- Verify API connection
- Check server logs for errors
- Test API credentials

**Dynamic fields not loading**
- Check JavaScript console for errors
- Verify AJAX endpoints are working
- Clear browser cache

### Debug Mode
Enable WordPress debug mode to see detailed error logs:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

Check `/wp-content/debug.log` for PayOp-related errors.

## Security

- All API communications use proper authentication
- Input validation and sanitization on all user inputs
- Nonce verification for AJAX requests
- IP whitelist validation for IPN requests
- Secure signature generation and validation

## Support

This is a personal-use plugin. For PayOp API documentation and account support, visit [PayOp.com](https://payop.com).

## License

GPL v2 or later
