/**
 * PayOp Admin Styles
 */

/* Admin container */
.payop-admin-container {
    max-width: 1200px;
    margin: 20px 0;
}

/* Admin header */
.payop-admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ddd;
}

.payop-admin-header h1 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
}

/* Buttons */
.payop-sync-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: #0073aa;
    color: white;
    padding: 8px 16px;
    text-decoration: none;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-size: 14px;
}

.payop-sync-button:hover {
    background: #005a87;
    color: white;
}

.payop-sync-button .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Payment methods table */
.payop-methods-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    margin-top: 20px;
}

.payop-methods-table th,
.payop-methods-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
    vertical-align: middle;
}

.payop-methods-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.payop-methods-table tr:hover {
    background: #f8f9fa;
}

.payop-methods-table tr:last-child td {
    border-bottom: none;
}

/* Method details */
.payop-method-logo {
    width: 40px;
    height: 40px;
    object-fit: contain;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.payop-method-title {
    font-weight: 500;
    color: #333;
}

.payop-method-type {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
    font-weight: 500;
}

.payop-method-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.payop-method-status.enabled {
    background: #d4edda;
    color: #155724;
}

.payop-method-status.disabled {
    background: #f8d7da;
    color: #721c24;
}

/* Action buttons */
.payop-method-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.payop-method-toggle {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 3px;
    border: 1px solid;
    cursor: pointer;
    text-decoration: none;
}

.payop-method-toggle.enabled {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
}

.payop-method-toggle.disabled {
    background: #28a745;
    color: white;
    border-color: #28a745;
}

.payop-method-toggle:hover {
    opacity: 0.8;
    color: white;
    text-decoration: none;
}

/* Configuration sections */
.payop-config-section {
    background: white;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    box-shadow: 0 1px 1px rgba(0,0,0,0.04);
}

.payop-config-section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.payop-config-field {
    margin-bottom: 20px;
}

.payop-config-field label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.payop-config-field input,
.payop-config-field select,
.payop-config-field textarea {
    width: 100%;
    max-width: 400px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.payop-config-field .description {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
    font-style: italic;
}

/* Statistics cards */
.payop-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.payop-stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
}

.payop-stat-number {
    font-size: 32px;
    font-weight: bold;
    color: #0073aa;
    margin-bottom: 5px;
}

.payop-stat-label {
    font-size: 14px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Notices */
.payop-notice {
    padding: 12px 15px;
    margin: 15px 0;
    border-radius: 4px;
    font-size: 14px;
    border-left: 4px solid;
}

.payop-notice.success {
    background: #d4edda;
    color: #155724;
    border-left-color: #28a745;
}

.payop-notice.error {
    background: #f8d7da;
    color: #721c24;
    border-left-color: #dc3545;
}

.payop-notice.warning {
    background: #fff3cd;
    color: #856404;
    border-left-color: #ffc107;
}

.payop-notice.info {
    background: #d1ecf1;
    color: #0c5460;
    border-left-color: #17a2b8;
}

/* Loading states */
.payop-loading {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: #666;
}

.payop-loading::after {
    content: "";
    width: 16px;
    height: 16px;
    border: 2px solid #ddd;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: payop-spin 1s linear infinite;
}

@keyframes payop-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Tooltips */
.payop-tooltip {
    position: absolute;
    background: #333;
    color: white;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 9999;
}

.payop-tooltip::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #333 transparent transparent transparent;
}

/* Empty state */
.payop-empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.payop-empty-state h3 {
    margin-bottom: 10px;
    color: #333;
}

.payop-empty-state p {
    margin-bottom: 20px;
}

/* Responsive design */
@media (max-width: 768px) {
    .payop-admin-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .payop-methods-table {
        font-size: 14px;
    }
    
    .payop-methods-table th,
    .payop-methods-table td {
        padding: 8px 10px;
    }
    
    .payop-method-logo {
        width: 30px;
        height: 30px;
    }
    
    .payop-method-actions {
        flex-direction: column;
        gap: 4px;
    }
    
    .payop-stats {
        grid-template-columns: 1fr;
    }
}

/* Print styles */
@media print {
    .payop-admin-header .button,
    .payop-method-actions {
        display: none;
    }
}
