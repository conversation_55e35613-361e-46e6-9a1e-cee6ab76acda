/**
 * PayOp Payment Gateway Frontend Styles
 */

/* Payment method selection */
.payop-payment-method-selector {
    margin-bottom: 20px;
}

.payop-payment-method-selector label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.payop-payment-method-selector select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

/* Dynamic fields container */
#payop-dynamic-fields {
    margin-top: 20px;
    padding: 20px;
    background: #f9f9f9;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
}

#payop-dynamic-fields:empty {
    display: none;
}

/* Field styling */
.payop-field {
    margin-bottom: 15px;
}

.payop-field label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

.payop-field label.required::after {
    content: " *";
    color: #e74c3c;
}

.payop-field input,
.payop-field select,
.payop-field textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.payop-field input:focus,
.payop-field select:focus,
.payop-field textarea:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
}

.payop-field input.error,
.payop-field select.error,
.payop-field textarea.error {
    border-color: #e74c3c;
    box-shadow: 0 0 0 1px #e74c3c;
}

.payop-field .field-description {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

/* Error messages */
.payop-error-message,
.field-error {
    display: block;
    color: #e74c3c;
    font-size: 12px;
    margin-top: 5px;
}

.payop-error {
    background: #f8d7da;
    color: #721c24;
    padding: 10px;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    margin: 10px 0;
}

/* Loading state */
.payop-loading {
    text-align: center;
    padding: 20px;
    color: #666;
}

.payop-loading::after {
    content: "";
    display: inline-block;
    width: 20px;
    height: 20px;
    margin-left: 10px;
    border: 2px solid #ddd;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: payop-spin 1s linear infinite;
}

@keyframes payop-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Admin styles */
.payop-admin-container {
    max-width: 1200px;
    margin: 20px 0;
}

.payop-admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
}

.payop-admin-header h1 {
    margin: 0;
}

.payop-sync-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.payop-sync-button .dashicons {
    font-size: 16px;
}

/* Payment methods table */
.payop-methods-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.payop-methods-table th,
.payop-methods-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.payop-methods-table th {
    background: #f8f9fa;
    font-weight: bold;
    color: #333;
}

.payop-methods-table tr:hover {
    background: #f8f9fa;
}

.payop-method-logo {
    width: 40px;
    height: 40px;
    object-fit: contain;
    border-radius: 4px;
}

.payop-method-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.payop-method-status.enabled {
    background: #d4edda;
    color: #155724;
}

.payop-method-status.disabled {
    background: #f8d7da;
    color: #721c24;
}

.payop-method-toggle {
    margin-right: 8px;
}

/* Configuration section */
.payop-config-section {
    background: white;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
}

.payop-config-section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
}

.payop-config-field {
    margin-bottom: 15px;
}

.payop-config-field label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.payop-config-field input,
.payop-config-field select,
.payop-config-field textarea {
    width: 100%;
    max-width: 400px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.payop-config-field .description {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

/* Success/error notices */
.payop-notice {
    padding: 10px 15px;
    margin: 10px 0;
    border-radius: 4px;
    font-size: 14px;
}

.payop-notice.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.payop-notice.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.payop-notice.warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* Responsive design */
@media (max-width: 768px) {
    .payop-admin-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .payop-methods-table {
        font-size: 14px;
    }
    
    .payop-methods-table th,
    .payop-methods-table td {
        padding: 8px;
    }
    
    .payop-method-logo {
        width: 30px;
        height: 30px;
    }
}

/* Accessibility improvements */
.payop-field input:focus,
.payop-field select:focus,
.payop-field textarea:focus {
    outline: 2px solid #0073aa;
    outline-offset: 2px;
}

.payop-error[role="alert"] {
    border-left: 4px solid #e74c3c;
    padding-left: 16px;
}

/* Print styles */
@media print {
    .payop-admin-header .button,
    .payop-method-toggle {
        display: none;
    }
}
