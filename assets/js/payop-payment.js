/**
 * PayOp Payment Gateway Frontend JavaScript
 * Handles dynamic payment method selection and field rendering
 */

jQuery(document).ready(function($) {
    'use strict';

    var PayOpPayment = {
        init: function() {
            this.bindEvents();
            this.loadPaymentMethods();
        },

        bindEvents: function() {
            // Handle payment method selection change
            $(document).on('change', '#payop_payment_method', this.onPaymentMethodChange.bind(this));
            
            // Handle form submission
            $('form.checkout').on('checkout_place_order_payop', this.validatePaymentForm.bind(this));
            
            // Handle admin payment method toggle
            $(document).on('click', '.payop-method-toggle', this.togglePaymentMethod.bind(this));
        },

        onPaymentMethodChange: function(e) {
            var selectedMethod = $(e.target).val();
            var $container = $('#payop-dynamic-fields');
            
            if (!selectedMethod) {
                $container.empty();
                return;
            }

            // Show loading state
            $container.html('<div class="payop-loading">Loading payment fields...</div>');

            // Fetch fields for selected payment method
            $.ajax({
                url: payop_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'payop_get_payment_fields',
                    payment_method: selectedMethod,
                    nonce: payop_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        $container.html(response.data.html);
                        PayOpPayment.initializeFields();
                    } else {
                        $container.html('<div class="payop-error">Error loading payment fields</div>');
                    }
                },
                error: function() {
                    $container.html('<div class="payop-error">Error loading payment fields</div>');
                }
            });
        },

        initializeFields: function() {
            // Initialize any special field types (date pickers, etc.)
            $('#payop-dynamic-fields input[type="date"]').each(function() {
                // Add date picker functionality if needed
            });

            // Initialize phone number formatting
            $('#payop-dynamic-fields input[data-type="phone"]').each(function() {
                $(this).on('input', function() {
                    // Basic phone number formatting
                    var value = $(this).val().replace(/\D/g, '');
                    $(this).val(value);
                });
            });

            // Initialize email validation
            $('#payop-dynamic-fields input[type="email"]').each(function() {
                $(this).on('blur', function() {
                    var email = $(this).val();
                    if (email && !PayOpPayment.isValidEmail(email)) {
                        $(this).addClass('error');
                        $(this).siblings('.field-error').remove();
                        $(this).after('<span class="field-error">Please enter a valid email address</span>');
                    } else {
                        $(this).removeClass('error');
                        $(this).siblings('.field-error').remove();
                    }
                });
            });
        },

        validatePaymentForm: function() {
            var isValid = true;
            var $fields = $('#payop-dynamic-fields input[required], #payop-dynamic-fields select[required]');

            // Clear previous errors
            $('.payop-error-message').remove();
            $fields.removeClass('error');

            // Validate required fields
            $fields.each(function() {
                var $field = $(this);
                var value = $field.val();

                if (!value || value.trim() === '') {
                    isValid = false;
                    $field.addClass('error');
                    $field.after('<span class="payop-error-message">This field is required</span>');
                }
            });

            // Validate email fields
            $('#payop-dynamic-fields input[type="email"]').each(function() {
                var email = $(this).val();
                if (email && !PayOpPayment.isValidEmail(email)) {
                    isValid = false;
                    $(this).addClass('error');
                    $(this).siblings('.payop-error-message').remove();
                    $(this).after('<span class="payop-error-message">Please enter a valid email address</span>');
                }
            });

            if (!isValid) {
                $('html, body').animate({
                    scrollTop: $('.error').first().offset().top - 100
                }, 500);
            }

            return isValid;
        },

        loadPaymentMethods: function() {
            var $select = $('#payop_payment_method');
            if ($select.length === 0) return;

            // Load available payment methods
            $.ajax({
                url: payop_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'payop_get_payment_methods',
                    nonce: payop_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        var methods = response.data.methods;
                        $select.empty().append('<option value="">Select payment method</option>');
                        
                        $.each(methods, function(key, method) {
                            if (method.enabled) {
                                $select.append(
                                    $('<option></option>')
                                        .attr('value', method.id)
                                        .text(method.title)
                                );
                            }
                        });
                    }
                }
            });
        },

        togglePaymentMethod: function(e) {
            e.preventDefault();
            var $button = $(e.currentTarget);
            var methodId = $button.data('method-id');
            var enabled = $button.data('enabled') === '1' ? '0' : '1';

            $.ajax({
                url: payop_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'payop_toggle_payment_method',
                    method_id: methodId,
                    enabled: enabled,
                    nonce: payop_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        $button.data('enabled', enabled);
                        $button.text(enabled === '1' ? 'Disable' : 'Enable');
                        $button.toggleClass('button-secondary button-primary');
                        
                        // Update status indicator
                        var $status = $button.closest('tr').find('.payop-method-status');
                        $status.text(enabled === '1' ? 'Enabled' : 'Disabled');
                        $status.toggleClass('enabled disabled');
                    } else {
                        alert('Error updating payment method: ' + (response.data.message || 'Unknown error'));
                    }
                },
                error: function() {
                    alert('Error updating payment method');
                }
            });
        },

        isValidEmail: function(email) {
            var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
    };

    // Initialize when document is ready
    PayOpPayment.init();

    // Make PayOpPayment available globally
    window.PayOpPayment = PayOpPayment;
});
