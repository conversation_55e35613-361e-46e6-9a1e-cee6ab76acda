/**
 * PayOp Admin JavaScript
 * Handles admin panel functionality
 */

jQuery(document).ready(function($) {
    'use strict';

    var PayOpAdmin = {
        init: function() {
            this.bindEvents();
            this.initializeTooltips();
        },

        bindEvents: function() {
            // Sync payment methods button
            $(document).on('click', '#payop-sync-methods', this.syncPaymentMethods.bind(this));
            
            // Save configuration
            $(document).on('click', '#payop-save-config', this.saveConfiguration.bind(this));
            
            // Test API connection
            $(document).on('click', '#payop-test-connection', this.testConnection.bind(this));
            
            // Toggle payment method
            $(document).on('click', '.payop-method-toggle', this.togglePaymentMethod.bind(this));
            
            // Show/hide API fields based on environment
            $(document).on('change', '#payop_environment', this.toggleEnvironmentFields.bind(this));
            
            // Initialize environment fields on page load
            this.toggleEnvironmentFields();
        },

        syncPaymentMethods: function(e) {
            e.preventDefault();
            var $button = $(e.currentTarget);
            var originalText = $button.text();
            
            $button.prop('disabled', true).text('Syncing...');
            this.showNotice('Syncing payment methods...', 'info');

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'payop_sync_payment_methods',
                    nonce: payop_admin_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        PayOpAdmin.showNotice('Payment methods synced successfully!', 'success');
                        // Reload the page to show updated methods
                        setTimeout(function() {
                            location.reload();
                        }, 1500);
                    } else {
                        PayOpAdmin.showNotice('Error syncing payment methods: ' + (response.data.message || 'Unknown error'), 'error');
                    }
                },
                error: function() {
                    PayOpAdmin.showNotice('Error syncing payment methods. Please try again.', 'error');
                },
                complete: function() {
                    $button.prop('disabled', false).text(originalText);
                }
            });
        },

        saveConfiguration: function(e) {
            e.preventDefault();
            var $form = $('#payop-config-form');
            var $button = $(e.currentTarget);
            var originalText = $button.text();
            
            $button.prop('disabled', true).text('Saving...');

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: $form.serialize() + '&action=payop_save_config&nonce=' + payop_admin_ajax.nonce,
                success: function(response) {
                    if (response.success) {
                        PayOpAdmin.showNotice('Configuration saved successfully!', 'success');
                    } else {
                        PayOpAdmin.showNotice('Error saving configuration: ' + (response.data.message || 'Unknown error'), 'error');
                    }
                },
                error: function() {
                    PayOpAdmin.showNotice('Error saving configuration. Please try again.', 'error');
                },
                complete: function() {
                    $button.prop('disabled', false).text(originalText);
                }
            });
        },

        testConnection: function(e) {
            e.preventDefault();
            var $button = $(e.currentTarget);
            var originalText = $button.text();
            
            $button.prop('disabled', true).text('Testing...');

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'payop_test_connection',
                    nonce: payop_admin_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        PayOpAdmin.showNotice('API connection successful!', 'success');
                    } else {
                        PayOpAdmin.showNotice('API connection failed: ' + (response.data.message || 'Unknown error'), 'error');
                    }
                },
                error: function() {
                    PayOpAdmin.showNotice('Error testing connection. Please try again.', 'error');
                },
                complete: function() {
                    $button.prop('disabled', false).text(originalText);
                }
            });
        },

        togglePaymentMethod: function(e) {
            e.preventDefault();
            var $button = $(e.currentTarget);
            var methodId = $button.data('method-id');
            var enabled = $button.data('enabled') === '1' ? '0' : '1';
            var originalText = $button.text();

            $button.prop('disabled', true);

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'payop_toggle_payment_method',
                    method_id: methodId,
                    enabled: enabled,
                    nonce: payop_admin_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        $button.data('enabled', enabled);
                        $button.text(enabled === '1' ? 'Disable' : 'Enable');
                        $button.toggleClass('button-secondary button-primary');
                        
                        // Update status indicator
                        var $status = $button.closest('tr').find('.payop-method-status');
                        $status.text(enabled === '1' ? 'Enabled' : 'Disabled');
                        $status.toggleClass('enabled disabled');
                        
                        PayOpAdmin.showNotice('Payment method updated successfully!', 'success');
                    } else {
                        PayOpAdmin.showNotice('Error updating payment method: ' + (response.data.message || 'Unknown error'), 'error');
                    }
                },
                error: function() {
                    PayOpAdmin.showNotice('Error updating payment method. Please try again.', 'error');
                },
                complete: function() {
                    $button.prop('disabled', false);
                }
            });
        },

        toggleEnvironmentFields: function() {
            var environment = $('#payop_environment').val();
            var $liveFields = $('.payop-live-fields');
            var $testFields = $('.payop-test-fields');

            if (environment === 'live') {
                $liveFields.show();
                $testFields.hide();
            } else {
                $liveFields.hide();
                $testFields.show();
            }
        },

        showNotice: function(message, type) {
            // Remove existing notices
            $('.payop-admin-notice').remove();
            
            var noticeClass = 'payop-notice payop-admin-notice';
            if (type) {
                noticeClass += ' ' + type;
            }
            
            var $notice = $('<div class="' + noticeClass + '">' + message + '</div>');
            $('.payop-admin-container').prepend($notice);
            
            // Auto-hide success notices after 5 seconds
            if (type === 'success' || type === 'info') {
                setTimeout(function() {
                    $notice.fadeOut(500, function() {
                        $(this).remove();
                    });
                }, 5000);
            }
            
            // Scroll to notice
            $('html, body').animate({
                scrollTop: $notice.offset().top - 100
            }, 300);
        },

        initializeTooltips: function() {
            // Simple tooltip implementation
            $('[data-tooltip]').each(function() {
                var $element = $(this);
                var tooltip = $element.data('tooltip');
                
                $element.on('mouseenter', function() {
                    var $tooltip = $('<div class="payop-tooltip">' + tooltip + '</div>');
                    $('body').append($tooltip);
                    
                    var offset = $element.offset();
                    $tooltip.css({
                        position: 'absolute',
                        top: offset.top - $tooltip.outerHeight() - 5,
                        left: offset.left + ($element.outerWidth() / 2) - ($tooltip.outerWidth() / 2),
                        zIndex: 9999
                    });
                });
                
                $element.on('mouseleave', function() {
                    $('.payop-tooltip').remove();
                });
            });
        },

        confirmAction: function(message, callback) {
            if (confirm(message)) {
                callback();
            }
        }
    };

    // Initialize admin functionality
    PayOpAdmin.init();

    // Make PayOpAdmin available globally
    window.PayOpAdmin = PayOpAdmin;
});
