<?php
/**
 * Plugin Name: PayOp Personal Payment Gateway
 * Plugin URI: https://github.com/your-username/payop-personal
 * Description: Personal PayOp WooCommerce payment gateway with 122+ payment methods support
 * Version: 1.0.0
 * Author: Personal Use
 * Author URI: https://payop.com
 * Requires at least: 6.0
 * Tested up to: 6.4
 * WC requires at least: 8.0
 * WC tested up to: 8.5
 * Requires PHP: 8.0
 * License: GPL v2 or later
 * Text Domain: payop-personal-gateway
 * Domain Path: /languages
 * WC-HPOS-Support: yes
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('PAYOP_WC_VERSION', '1.0.0');
define('PAYOP_WC_PLUGIN_FILE', __FILE__);
define('PAYOP_WC_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('PAYOP_WC_PLUGIN_URL', plugin_dir_url(__FILE__));
define('PAYOP_WC_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main PayOp WooCommerce Plugin Class
 */
class PayOp_WooCommerce {
    
    /**
     * Plugin instance
     */
    private static $instance = null;
    
    /**
     * Get plugin instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('plugins_loaded', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    /**
     * Initialize the plugin
     */
    public function init() {
        // Check if WooCommerce is active
        if (!class_exists('WooCommerce')) {
            add_action('admin_notices', array($this, 'woocommerce_missing_notice'));
            return;
        }
        
        // Load plugin files
        $this->load_files();
        
        // Initialize payment gateway
        add_filter('woocommerce_payment_gateways', array($this, 'add_gateway'));
        
        // Declare HPOS compatibility
        add_action('before_woocommerce_init', array($this, 'declare_hpos_compatibility'));
        
        // Load text domain
        add_action('init', array($this, 'load_textdomain'));
        
        // Admin hooks
        if (is_admin()) {
            add_action('admin_menu', array($this, 'add_admin_menu'));
            add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        }
        
        // Frontend hooks
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_scripts'));
    }
    
    /**
     * Load required files
     */
    private function load_files() {
        // Core classes
        require_once PAYOP_WC_PLUGIN_DIR . 'includes/class-payop-api-client.php';
        require_once PAYOP_WC_PLUGIN_DIR . 'includes/class-payop-gateway.php';
        require_once PAYOP_WC_PLUGIN_DIR . 'includes/class-payop-payment-methods.php';
        require_once PAYOP_WC_PLUGIN_DIR . 'includes/class-payop-field-manager.php';
        require_once PAYOP_WC_PLUGIN_DIR . 'includes/class-payop-ipn-handler.php';
        
        // Admin classes
        if (is_admin()) {
            require_once PAYOP_WC_PLUGIN_DIR . 'includes/admin/class-payop-admin.php';
        }
    }
    
    /**
     * Add PayOp gateway to WooCommerce
     */
    public function add_gateway($gateways) {
        $gateways[] = 'PayOp_WC_Gateway';
        return $gateways;
    }
    
    /**
     * Declare HPOS compatibility
     */
    public function declare_hpos_compatibility() {
        if (class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil')) {
            \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility('custom_order_tables', __FILE__, true);
        }
    }
    
    /**
     * Load plugin text domain
     */
    public function load_textdomain() {
        load_plugin_textdomain('payop-personal-gateway', false, dirname(PAYOP_WC_PLUGIN_BASENAME) . '/languages');
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        if (class_exists('PayOp_Admin')) {
            $admin = new PayOp_Admin();
            $admin->add_menu();
        }
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        // Only load on PayOp admin pages
        if (strpos($hook, 'payop') === false) {
            return;
        }
        
        wp_enqueue_style(
            'payop-admin-style',
            PAYOP_WC_PLUGIN_URL . 'assets/css/payop-payment.css',
            array(),
            PAYOP_WC_VERSION
        );
        
        wp_enqueue_script(
            'payop-admin-script',
            PAYOP_WC_PLUGIN_URL . 'assets/js/payop-admin.js',
            array('jquery'),
            PAYOP_WC_VERSION,
            true
        );
        
        wp_localize_script('payop-admin-script', 'payop_admin_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('payop_admin_nonce')
        ));
    }
    
    /**
     * Enqueue frontend scripts and styles
     */
    public function enqueue_frontend_scripts() {
        // Only load on checkout and cart pages
        if (!is_checkout() && !is_cart()) {
            return;
        }
        
        wp_enqueue_style(
            'payop-payment-style',
            PAYOP_WC_PLUGIN_URL . 'assets/css/payop-payment.css',
            array(),
            PAYOP_WC_VERSION
        );
        
        wp_enqueue_script(
            'payop-payment-script',
            PAYOP_WC_PLUGIN_URL . 'assets/js/payop-payment.js',
            array('jquery'),
            PAYOP_WC_VERSION,
            true
        );
        
        wp_localize_script('payop-payment-script', 'payop_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('payop_payment_nonce')
        ));
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create database tables
        $this->create_tables();
        
        // Set default options
        $this->set_default_options();
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clean up if needed
        flush_rewrite_rules();
    }
    
    /**
     * Create plugin database tables
     */
    private function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Payment methods table
        $payment_methods_table = $wpdb->prefix . 'payop_payment_methods';
        $sql1 = "CREATE TABLE $payment_methods_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            identifier int(11) NOT NULL,
            type varchar(50) NOT NULL,
            title varchar(255) NOT NULL,
            logo varchar(500),
            currencies text,
            countries text,
            config text,
            is_enabled tinyint(1) DEFAULT 1,
            display_order int(11) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY identifier (identifier),
            KEY type (type),
            KEY is_enabled (is_enabled)
        ) $charset_collate;";
        
        // Transactions table
        $transactions_table = $wpdb->prefix . 'payop_transactions';
        $sql2 = "CREATE TABLE $transactions_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            order_id bigint(20) unsigned NOT NULL,
            invoice_id varchar(100),
            transaction_id varchar(100),
            payment_method_id int(11),
            amount decimal(10,2),
            currency varchar(3),
            status varchar(20),
            customer_data text,
            api_response text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY order_id (order_id),
            KEY invoice_id (invoice_id),
            KEY transaction_id (transaction_id),
            KEY status (status)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql1);
        dbDelta($sql2);
        
        // Update database version
        update_option('payop_db_version', '1.0');
    }
    
    /**
     * Set default plugin options
     */
    private function set_default_options() {
        $defaults = array(
            'payop_version' => PAYOP_WC_VERSION,
            'payop_payment_methods_cache' => array(),
            'payop_last_method_sync' => 0
        );
        
        foreach ($defaults as $key => $value) {
            if (get_option($key) === false) {
                update_option($key, $value);
            }
        }
    }
    
    /**
     * WooCommerce missing notice
     */
    public function woocommerce_missing_notice() {
        echo '<div class="notice notice-error"><p><strong>' . 
             esc_html__('PayOp Personal Payment Gateway requires WooCommerce to be installed and active.', 'payop-personal-gateway') . 
             '</strong></p></div>';
    }
}

// Initialize the plugin
PayOp_WooCommerce::get_instance();
